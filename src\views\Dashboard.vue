<template>
  <div class="dashboard-layout">
    <!-- 顶部导航栏 -->
    <header class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <div class="logo-section">
            <el-icon size="28" color="white"><Document /></el-icon>
            <h1>纸质文件电子化系统</h1>
          </div>
        </div>

        <div class="header-right">
          <div class="header-actions">
            <!-- 通知按钮 -->
            <el-badge :value="3" class="notification-badge">
              <el-button circle size="large" class="header-btn">
                <el-icon size="18"><Bell /></el-icon>
              </el-button>
            </el-badge>

            <!-- 用户下拉菜单 -->
            <el-dropdown @command="handleCommand" class="user-dropdown">
              <div class="user-info">
                <el-avatar size="36" class="user-avatar">
                  {{ userInfo.username?.charAt(0)?.toUpperCase() || 'U' }}
                </el-avatar>
                <div class="user-details">
                  <span class="username">{{ userInfo.username || '用户' }}</span>
                  <span class="user-role">管理员</span>
                </div>
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    系统设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </header>

    <!-- 主体内容 -->
    <div class="dashboard-body">
      <!-- 侧边栏 -->
      <aside class="sidebar">
        <nav class="sidebar-nav">
          <div class="nav-section">
            <div class="nav-title">主要功能</div>
            <ul class="nav-menu">
              <li :class="['nav-item', { active: activeMenu === 'dashboard' }]" @click="setActiveMenu('dashboard')">
                <el-icon><Odometer /></el-icon>
                <span>仪表板</span>
              </li>
              <li :class="['nav-item', { active: activeMenu === 'ocr' }]" @click="setActiveMenu('ocr')">
                <el-icon><Document /></el-icon>
                <span>OCR识别</span>
              </li>
              <li :class="['nav-item', { active: activeMenu === 'history' }]" @click="setActiveMenu('history')">
                <el-icon><Clock /></el-icon>
                <span>历史记录</span>
              </li>
              <li :class="['nav-item', { active: activeMenu === 'search' }]" @click="setActiveMenu('search')">
                <el-icon><Search /></el-icon>
                <span>文档检索</span>
              </li>
              <li :class="['nav-item', { active: activeMenu === 'files' }]" @click="setActiveMenu('files')">
                <el-icon><Folder /></el-icon>
                <span>文件管理</span>
              </li>
            </ul>
          </div>

        </nav>
      </aside>

      <!-- 主内容区域 -->
      <main class="main-content">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-section">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>首页</el-breadcrumb-item>
            <el-breadcrumb-item>{{ getMenuTitle(activeMenu) }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon primary">
              <el-icon size="24"><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">1,234</div>
              <div class="stat-label">已处理文档</div>
            </div>
            <div class="stat-trend up">
              <el-icon><TrendCharts /></el-icon>
              <span>+12%</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon success">
              <el-icon size="24"><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">98.5%</div>
              <div class="stat-label">识别准确率</div>
            </div>
            <div class="stat-trend up">
              <el-icon><TrendCharts /></el-icon>
              <span>+2.1%</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon warning">
              <el-icon size="24"><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">2.3s</div>
              <div class="stat-label">平均处理时间</div>
            </div>
            <div class="stat-trend down">
              <el-icon><TrendCharts /></el-icon>
              <span>-15%</span>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon info">
              <el-icon size="24"><Folder /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">567</div>
              <div class="stat-label">存储文件</div>
            </div>
            <div class="stat-trend up">
              <el-icon><TrendCharts /></el-icon>
              <span>+8%</span>
            </div>
          </div>
        </div>

        <!-- 功能区域 -->
        <div class="content-grid">
          <!-- 快速操作 -->
          <div class="content-card">
            <div class="card-header">
              <h3>快速操作</h3>
              <p>选择您需要的功能开始工作</p>
            </div>
            <div class="quick-actions">
              <div class="action-item primary" @click="startOCR">
                <div class="action-icon">
                  <el-icon size="32"><Plus /></el-icon>
                </div>
                <div class="action-content">
                  <h4>开始OCR识别</h4>
                  <p>上传文档进行文字识别</p>
                </div>
              </div>

              <div class="action-item success" @click="viewHistory">
                <div class="action-icon">
                  <el-icon size="32"><Clock /></el-icon>
                </div>
                <div class="action-content">
                  <h4>查看历史记录</h4>
                  <p>浏览已处理的文档</p>
                </div>
              </div>

              <div class="action-item warning" @click="searchDocuments">
                <div class="action-icon">
                  <el-icon size="32"><Search /></el-icon>
                </div>
                <div class="action-content">
                  <h4>文档检索</h4>
                  <p>搜索已处理的文档内容</p>
                </div>
              </div>

              <div class="action-item info" @click="manageFiles">
                <div class="action-icon">
                  <el-icon size="32"><Folder /></el-icon>
                </div>
                <div class="action-content">
                  <h4>文件管理</h4>
                  <p>管理您的文档文件</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 最近活动 -->
          <div class="content-card">
            <div class="card-header">
              <h3>最近活动</h3>
              <p>查看最新的系统活动</p>
            </div>
            <div class="activity-list">
              <div class="activity-item">
                <div class="activity-icon success">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">文档识别完成</div>
                  <div class="activity-desc">合同文档.pdf 已成功识别</div>
                  <div class="activity-time">2分钟前</div>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-icon primary">
                  <el-icon><Upload /></el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">文件上传</div>
                  <div class="activity-desc">发票扫描件.jpg 上传成功</div>
                  <div class="activity-time">5分钟前</div>
                </div>
              </div>

              <div class="activity-item">
                <div class="activity-icon warning">
                  <el-icon><Download /></el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">文件导出</div>
                  <div class="activity-desc">识别结果.docx 导出完成</div>
                  <div class="activity-time">10分钟前</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 文档管理区域 -->
        <div class="document-management-section">
          <div class="content-card">
            <div class="card-header">
              <div class="header-with-actions">
                <div>
                  <h3>文档管理</h3>
                  <p>管理系统中的所有文档</p>
                </div>
                <div class="header-actions">
                  <el-button
                    type="primary"
                    :icon="Refresh"
                    @click="loadDocuments"
                    :loading="loadingDocuments"
                    size="small"
                  >
                    刷新
                  </el-button>
                </div>
              </div>
            </div>

            <div class="document-content">
              <!-- 加载状态 -->
              <div v-if="loadingDocuments" class="loading-container">
                <el-icon class="is-loading" size="24"><Loading /></el-icon>
                <span>正在加载文档列表...</span>
              </div>

              <!-- 错误状态 -->
              <div v-else-if="documentsError" class="error-container">
                <el-icon size="24" color="#f56c6c"><Warning /></el-icon>
                <span>{{ documentsError }}</span>
                <el-button type="text" @click="loadDocuments">重试</el-button>
              </div>

              <!-- 空状态 -->
              <div v-else-if="!documents.length" class="empty-container">
                <el-icon size="48" color="#c0c4cc"><Document /></el-icon>
                <p>暂无文档</p>
                <el-button type="primary" @click="startOCR">开始上传文档</el-button>
              </div>

              <!-- 文档列表 -->
              <div v-else class="document-list">
                <div
                  v-for="doc in documents"
                  :key="doc.id || doc.doc_name"
                  class="document-item"
                  @click="viewDocument(doc)"
                >
                  <div class="document-icon">
                    <el-icon size="20"><Document /></el-icon>
                  </div>
                  <div class="document-info">
                    <div class="document-name">{{ doc.doc_name || doc.name }}</div>
                    <div class="document-meta">
                      <span v-if="doc.id">ID: {{ doc.id }}</span>
                      <span v-if="doc.file_count">文件数: {{ doc.file_count }}</span>
                      <span v-if="doc.created_at">创建时间: {{ formatDate(doc.created_at) }}</span>
                    </div>
                  </div>
                  <div class="document-actions">
                    <el-button
                      type="text"
                      size="small"
                      @click.stop="viewDocumentDetails(doc)"
                    >
                      查看详情
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User,
  ArrowDown,
  Document,
  Clock,
  Folder,
  Setting,
  Check,
  Plus,
  Bell,
  SwitchButton,
  Odometer,
  QuestionFilled,
  TrendCharts,
  Upload,
  Download,
  Search,
  Refresh,
  Loading,
  Warning
} from '@element-plus/icons-vue'
import { ocrAPI } from '../services/api'

const router = useRouter()
const userInfo = ref({})
const activeMenu = ref('dashboard')

// 文档管理相关数据
const documents = ref([])
const loadingDocuments = ref(false)
const documentsError = ref('')

onMounted(() => {
  // 获取用户信息
  const user = localStorage.getItem('user')
  if (user) {
    userInfo.value = JSON.parse(user)
  }

  // 加载文档列表
  loadDocuments()
})

// 设置活动菜单
const setActiveMenu = (menu) => {
  activeMenu.value = menu
  handleMenuSelect(menu)
}

// 获取菜单标题
const getMenuTitle = (menu) => {
  const titles = {
    dashboard: '仪表板',
    ocr: 'OCR识别',
    history: '历史记录',
    search: '文档检索',
    files: '文件管理',
    settings: '系统设置',
    help: '帮助中心'
  }
  return titles[menu] || '仪表板'
}

// 处理下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 清除本地存储
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        
        ElMessage.success('已退出登录')
        router.push('/')
      } catch {
        // 用户取消操作
      }
      break
  }
}

// 处理菜单选择
const handleMenuSelect = (index) => {
  switch (index) {
    case 'dashboard':
      // 当前页面，无需跳转
      break
    case 'ocr':
      router.push('/ocr')
      break
    case 'history':
      router.push('/history')
      break
    case 'search':
      router.push('/search')
      break
    case 'files':
      router.push('/documents')
      break
    case 'settings':
      ElMessage.info('系统设置功能开发中...')
      break
    case 'help':
      ElMessage.info('帮助中心功能开发中...')
      break
  }
}

// 快速操作函数
const startOCR = () => {
  router.push('/ocr')
}

const viewHistory = () => {
  router.push('/history')
}

const searchDocuments = () => {
  router.push('/search')
}

const manageFiles = () => {
  router.push('/documents')
}

// 文档管理相关方法
const loadDocuments = async () => {
  loadingDocuments.value = true
  documentsError.value = ''

  try {
    console.log('📄 开始加载文档列表...')
    const response = await ocrAPI.getDocNames()

    if (response && response.docs && Array.isArray(response.docs)) {
      // 处理 { "docs": [...] } 格式
      documents.value = response.docs
      console.log('✅ 文档列表加载成功:', {
        数量: documents.value.length,
        文档: documents.value.map(doc => doc.doc_name || doc.name)
      })
      ElMessage.success(`成功加载 ${documents.value.length} 个文档`)
    } else if (response && response.data && Array.isArray(response.data)) {
      // 处理 { "data": [...] } 格式
      documents.value = response.data
      console.log('✅ 文档列表加载成功:', {
        数量: documents.value.length,
        文档: documents.value.map(doc => doc.doc_name || doc.name)
      })
      ElMessage.success(`成功加载 ${documents.value.length} 个文档`)
    } else if (response && Array.isArray(response)) {
      // 处理直接返回数组格式
      documents.value = response
      console.log('✅ 文档列表加载成功:', {
        数量: documents.value.length,
        文档: documents.value.map(doc => doc.doc_name || doc.name)
      })
      ElMessage.success(`成功加载 ${documents.value.length} 个文档`)
    } else {
      documents.value = []
      console.log('⚠️ API返回空数据或格式异常:', response)
    }
  } catch (error) {
    console.error('❌ 加载文档列表失败:', error)
    documentsError.value = error.response?.data?.message || error.message || '加载文档列表失败'
    ElMessage.error(documentsError.value)
  } finally {
    loadingDocuments.value = false
  }
}

const viewDocument = (doc) => {
  console.log('📄 查看文档:', doc)
  // 跳转到历史记录页面，并传递文档名称作为筛选条件
  router.push({
    path: '/history',
    query: { doc_name: doc.doc_name || doc.name }
  })
}

const viewDocumentDetails = (doc) => {
  console.log('📄 查看文档详情:', doc)
  ElMessage.info(`查看文档详情: ${doc.doc_name || doc.name}`)
  // 这里可以打开一个详情对话框或跳转到详情页面
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return dateString
  }
}
</script>

<style scoped>
/* 整体布局 */
.dashboard-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

/* 顶部导航栏 */
.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  height: 64px;
  max-width: 1920px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-section h1 {
  color: white;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
}

.header-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notification-badge {
  margin-right: 8px;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.2);
}

.user-avatar {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.username {
  color: white;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.user-role {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: 1.2;
}

.dropdown-icon {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

/* 主体内容 */
.dashboard-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 侧边栏 */
.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
}

.sidebar-nav {
  padding: 24px 0;
}

.nav-section {
  margin-bottom: 32px;
}

.nav-title {
  padding: 0 24px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s ease;
  border-right: 3px solid transparent;
}

.nav-item:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.nav-item.active {
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  color: #667eea;
  border-right-color: #667eea;
  font-weight: 500;
}

.nav-item .el-icon {
  font-size: 18px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px;
  max-width: calc(100vw - 280px);
}

/* 面包屑 */
.breadcrumb-section {
  margin-bottom: 24px;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.info {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.up {
  color: #10b981;
}

.stat-trend.down {
  color: #ef4444;
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 32px;
}

.content-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.card-header {
  margin-bottom: 24px;
}

.card-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.card-header p {
  color: #6b7280;
  margin: 0;
  font-size: 14px;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.action-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-item.primary {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-color: rgba(102, 126, 234, 0.2);
}

.action-item.success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  border-color: rgba(16, 185, 129, 0.2);
}

.action-item.warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
  border-color: rgba(245, 158, 11, 0.2);
}

.action-item.info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.1) 100%);
  border-color: rgba(59, 130, 246, 0.2);
}

.action-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  color: #667eea;
}

.action-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.action-content p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  background: #f9fafb;
  transition: all 0.2s ease;
}

.activity-item:hover {
  background: #f3f4f6;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.activity-icon.success {
  background: #10b981;
}

.activity-icon.primary {
  background: #667eea;
}

.activity-icon.warning {
  background: #f59e0b;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.activity-desc {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #9ca3af;
}

/* 文档管理区域 */
.document-management-section {
  margin-top: 32px;
}

.header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.document-content {
  min-height: 200px;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6b7280;
}

.loading-container .el-icon {
  margin-bottom: 12px;
}

.error-container {
  color: #f56c6c;
}

.error-container .el-icon {
  margin-bottom: 8px;
}

.error-container span {
  margin-bottom: 12px;
}

.empty-container .el-icon {
  margin-bottom: 16px;
}

.empty-container p {
  margin: 0 0 16px 0;
  font-size: 16px;
}

.document-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s ease;
}

.document-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.document-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.document-info {
  flex: 1;
}

.document-name {
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.document-meta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #6b7280;
}

.document-meta span {
  display: flex;
  align-items: center;
}

.document-actions {
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-body {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .main-content {
    max-width: 100%;
    padding: 16px;
  }

  .header-content {
    padding: 0 16px;
  }

  .user-details {
    display: none;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .content-card {
    padding: 20px;
  }
}
</style>
