# 纸质文件电子化系统

基于PaddleOCR的智能文档处理平台，提供高精度的文档识别和电子化服务。

## 🚀 功能特性

- **用户认证系统**: 支持用户注册、登录功能
- **现代化界面**: 基于Element Plus的美观用户界面
- **响应式设计**: 适配各种屏幕尺寸
- **路由管理**: 完整的前端路由系统
- **状态管理**: 用户状态持久化存储

## 🛠️ 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite 7.0
- **UI组件库**: Element Plus
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **图标库**: Element Plus Icons

## 📋 后端API接口

系统需要配合后端API使用，默认后端地址：`http://127.0.0.1:5000`

### 认证接口
- `POST /auth/register` - 用户注册
- `POST /auth/login` - 用户登录
- `GET /auth/user` - 获取用户信息

## 🔧 项目设置

### 安装依赖
```sh
npm install
```

### 开发环境运行
```sh
npm run dev
```

### 生产环境构建
```sh
npm run build
```

### 预览构建结果
```sh
npm run preview
```

## 📁 项目结构

```
src/
├── components/          # 可复用组件
├── composables/         # 组合式函数
│   └── useAuth.js      # 认证状态管理
├── router/             # 路由配置
│   └── index.js        # 路由定义
├── services/           # API服务
│   └── api.js          # HTTP请求封装
├── views/              # 页面组件
│   ├── Home.vue        # 首页/登录页面
│   ├── Register.vue    # 注册页面
│   └── Dashboard.vue   # 仪表板页面
├── assets/             # 静态资源
├── App.vue             # 根组件
└── main.js             # 应用入口
```

## 🎯 使用说明

1. **启动应用**: 运行 `npm run dev` 启动开发服务器
2. **访问系统**: 浏览器打开 `http://localhost:5173`
3. **用户注册**: 首次使用需要注册账号
4. **用户登录**: 使用注册的账号登录系统
5. **功能使用**: 登录后可使用各种文档处理功能

## 🔐 认证流程

1. 用户在注册页面创建账号
2. 系统将用户信息发送到后端API
3. 注册成功后跳转到登录页面
4. 用户输入凭据登录
5. 登录成功后获取token并存储
6. 后续请求自动携带token进行认证

## 🎨 界面预览

- **登录页面**: 简洁的登录表单，支持表单验证
- **注册页面**: 完整的注册流程，包含邮箱验证
- **仪表板**: 现代化的管理界面，为OCR功能预留空间

## 📝 开发说明

- 使用Vue 3 Composition API编写
- 采用Element Plus组件库
- 支持响应式设计
- 包含完整的错误处理
- 实现了路由守卫和权限控制
