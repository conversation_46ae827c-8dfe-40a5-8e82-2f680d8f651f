// OCR历史记录API测试工具
import { ocrAPI } from '../services/api'
import { getCookie, getSessionStatus } from './session'

/**
 * 测试历史记录API连接
 */
export const testHistoryAPI = async () => {
  try {
    console.log('Session状态:', getSessionStatus())
    
    const response = await ocrAPI.getHistory({
      page: 1,
      size: 10
    })
    
    console.log('历史记录API响应:', response)
    
    return {
      success: true,
      message: '历史记录API连接成功',
      data: response
    }
  } catch (error) {
    console.error('历史记录API测试失败:', error)
    
    let message = '历史记录API连接失败'
    if (error.code === 'ERR_NETWORK') {
      message = '无法连接到服务器 (GET /api/ocr/history)'
    } else if (error.response?.status === 401) {
      message = '未授权访问，请检查登录状态或session cookie'
    } else if (error.response?.status === 404) {
      message = 'API接口不存在 (/api/ocr/history)'
    } else if (error.response?.data?.message) {
      message = error.response.data.message
    }
    
    return {
      success: false,
      message,
      error
    }
  }
}

/**
 * 模拟历史记录数据（当API不可用时使用）
 */
export const generateMockHistoryData = (count = 20) => {
  const mockData = []
  const statuses = ['success', 'failed', 'processing']
  const docTypes = ['合同文档', '发票扫描', '身份证件', '报告文件', '表格数据']
  
  for (let i = 1; i <= count; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const docType = docTypes[Math.floor(Math.random() * docTypes.length)]
    
    mockData.push({
      id: i,
      doc_name: `${docType}_${i.toString().padStart(3, '0')}`,
      create_time: createTime.toISOString(),
      status: status,
      text_length: status === 'success' ? Math.floor(Math.random() * 2000) + 100 : 0,
      confidence: status === 'success' ? `${(Math.random() * 10 + 90).toFixed(1)}%` : '0%',
      text: status === 'success' ? 
        `这是${docType}的OCR识别结果示例...\n\n包含多行文本内容，展示文档的具体信息。\n识别时间: ${createTime.toLocaleString('zh-CN')}\n文档编号: DOC${i.toString().padStart(6, '0')}` : 
        '识别失败，无文本内容'
    })
  }
  
  return mockData.sort((a, b) => new Date(b.create_time) - new Date(a.create_time))
}

/**
 * 格式化API返回的历史记录数据
 */
export const formatHistoryResponse = (response) => {
  console.log('🔍 formatHistoryResponse 输入:', response)

  if (!response) {
    console.log('❌ 响应为空')
    return { list: [], total: 0 }
  }

  let list = []
  let total = 0
  let pagination = {}

  // 处理实际API返回格式: { history: [...], total: number, page: number, pages: number, size: number }
  if (response.history && Array.isArray(response.history)) {
    console.log('✅ 检测到 history 数组格式，长度:', response.history.length)
    list = response.history
    total = response.total || response.history.length
    pagination = {
      page: response.page || 1,
      pages: response.pages || 1,
      size: response.size || 20
    }
  } else if (Array.isArray(response)) {
    // 直接返回数组
    list = response
    total = response.length
  } else if (response.data && Array.isArray(response.data)) {
    // 格式: { data: [...], total: number }
    list = response.data
    total = response.total || response.data.length
  } else if (response.list && Array.isArray(response.list)) {
    // 格式: { list: [...], total: number }
    list = response.list
    total = response.total || response.list.length
  } else if (response.records && Array.isArray(response.records)) {
    // 格式: { records: [...], total: number }
    list = response.records
    total = response.total || response.records.length
  }

  // 格式化每个记录项，适配实际API返回的字段
  console.log('🔄 开始格式化记录项，数量:', list.length)

  const formattedList = list.map((item, index) => {
    const formatted = {
      id: item.id || `${item.doc_name}_${index}` || Math.random().toString(36).substring(2),
      docName: item.doc_name || item.docName || '未命名文档',
      createTime: item.upload_time || item.create_time || item.createTime || new Date().toISOString(),
      status: item.status || 'success', // 默认为成功状态
      textLength: item.text_length || item.textLength || (item.text ? item.text.length : 0) || 0,
      confidence: item.confidence || '95%', // 默认置信度
      textContent: item.text || item.textContent || item.content || '暂无识别内容',
      // 新增字段，来自实际API
      fileName: item.filename || item.fileName || '',
      filePath: item.file_path || item.filePath || '',
      fileSize: item.file_size || item.fileSize || 0,
      uploadTime: item.upload_time || item.uploadTime || item.createTime || ''
    }

    return formatted
  })

  const result = {
    list: formattedList,
    total,
    pagination
  }

  console.log('✅ formatHistoryResponse 输出:', {
    listLength: result.list.length,
    total: result.total,
    firstItem: result.list[0]
  })

  return result
}

/**
 * 检查session cookie是否存在
 */
export const checkSessionCookie = () => {
  const sessionCookie = getCookie('session')
  const hasSession = !!sessionCookie
  
  console.log('Session Cookie检查:', {
    exists: hasSession,
    value: sessionCookie ? `${sessionCookie.substring(0, 10)}...` : null,
    allCookies: document.cookie
  })
  
  return {
    hasSession,
    sessionCookie,
    recommendation: hasSession ? 
      '已找到session cookie，API调用应该正常' : 
      '未找到session cookie，可能需要先登录或手动设置cookie'
  }
}

/**
 * 手动设置测试用的session cookie
 */
export const setTestSessionCookie = () => {
  const testSessionId = `test_session_${Date.now()}`
  document.cookie = `session=${testSessionId}; path=/`
  
  console.log('已设置测试session cookie:', testSessionId)
  return testSessionId
}

/**
 * 获取历史记录API的完整测试报告
 */
export const getHistoryAPITestReport = async () => {
  const report = {
    timestamp: new Date().toISOString(),
    sessionCheck: checkSessionCookie(),
    apiTest: null,
    recommendations: []
  }
  
  // 测试API连接
  report.apiTest = await testHistoryAPI()
  
  // 生成建议
  if (!report.sessionCheck.hasSession) {
    report.recommendations.push('建议先登录系统或手动设置session cookie')
  }
  
  if (!report.apiTest.success) {
    if (report.apiTest.error?.code === 'ERR_NETWORK') {
      report.recommendations.push('请确保后端服务正在运行 (http://127.0.0.1:5000)')
    } else if (report.apiTest.error?.response?.status === 401) {
      report.recommendations.push('请检查登录状态或session cookie是否有效')
    } else if (report.apiTest.error?.response?.status === 404) {
      report.recommendations.push('请确认后端API路径是否正确 (/api/ocr/history)')
    }
  }
  
  if (report.recommendations.length === 0) {
    report.recommendations.push('历史记录API工作正常')
  }
  
  return report
}
