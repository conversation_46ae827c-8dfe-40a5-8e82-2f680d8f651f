import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: '', // 使用相对路径，通过Vite代理转发
  timeout: 60000, // 设置为1分钟超时，适应OCR处理时间
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: false // 暂时禁用凭据，避免CORS问题
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    let message = '请求失败'

    if (error.code === 'ERR_NETWORK') {
      message = '无法连接到服务器，请检查网络连接或联系管理员'
    } else if (error.response?.data?.msg) {
      message = error.response.data.msg
    } else if (error.response?.data?.message) {
      message = error.response.data.message
    } else if (error.message) {
      message = error.message
    }

    // 不在拦截器中显示错误消息，让调用方处理
    console.error('API请求错误:', error)

    // 如果是401错误，清除token并跳转到登录页
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/'
    }

    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  // 用户注册
  register(userData) {
    return api.post('/auth/register', userData)
  },

  // 用户登录
  login(credentials) {
    return api.post('/auth/login', credentials)
  },

  // 获取用户信息
  getUserInfo() {
    return api.get('/auth/user')
  }
}

// OCR相关API
export const ocrAPI = {
  // 上传图片进行OCR识别
  uploadImage(formData) {
    return api.post('/image/ocr/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000 // 2分钟超时，因为OCR处理需要较长时间
    })
  },

  // 获取OCR历史记录
  getHistory(params = {}) {
    return api.get('/api/ocr/history', { params })
  },

  // 获取单个历史记录详情
  getHistoryDetail(docName, filename) {
    console.log('📄 获取历史记录详情:', { docName, filename })
    // URL编码处理特殊字符
    const encodedDocName = encodeURIComponent(docName)
    const encodedFilename = encodeURIComponent(filename)
    return api.get(`/api/ocr/history/${encodedDocName}/${encodedFilename}`)
  },

  // 获取OCR结果详情
  getResult(id) {
    return api.get(`/api/ocr/result/${id}`)
  },

  // 删除OCR记录
  deleteResult(id) {
    return api.delete(`/api/ocr/result/${id}`)
  },

  // 下载PDF文档
  downloadPDF(docName) {
    console.log('📄 下载PDF文档:', { docName })
    // URL编码处理特殊字符
    const encodedDocName = encodeURIComponent(docName)
    const apiUrl = `/ocr/text_pdf/${encodedDocName}`
    console.log('📄 PDF下载API URL:', apiUrl)
    return api.get(apiUrl, {
      responseType: 'blob', // 重要：设置响应类型为blob
      timeout: 120000 // 2分钟超时，因为PDF生成可能需要较长时间
    })
  },

  // 获取所有文档名称和ID
  getDocNames() {
    console.log('📄 获取文档名称列表')
    return api.get('/api/ocr/doc_names')
  },

  // 下载原图文件
  downloadOriginalImage(docName, filename) {
    console.log('📄 下载原图文件:', { docName, filename })
    // URL编码处理特殊字符
    const encodedDocName = encodeURIComponent(docName)
    const encodedFilename = encodeURIComponent(filename)
    const apiUrl = `/image/ocr/download/${encodedDocName}/${encodedFilename}`
    console.log('📄 原图下载API URL:', apiUrl)
    return api.get(apiUrl, {
      responseType: 'blob', // 重要：设置响应类型为blob
      timeout: 60000 // 1分钟超时
    })
  },

  // 获取原图预览URL（用于图片预览）
  getOriginalImageUrl(docName, filename) {
    const encodedDocName = encodeURIComponent(docName)
    const encodedFilename = encodeURIComponent(filename)
    return `/image/ocr/download/${encodedDocName}/${encodedFilename}`
  }
}

// 检索相关API
export const searchAPI = {
  // 文档检索
  searchDocuments(query) {
    console.log('🔍 执行文档检索:', { query })
    return api.post('/search/doc', { query })
  },

  // 语义相似检索
  semanticSearch(query, k = 5) {
    console.log('🧠 执行语义检索:', { query, k })
    return api.post('/search/semantic', { query, k })
  }
}

export default api
