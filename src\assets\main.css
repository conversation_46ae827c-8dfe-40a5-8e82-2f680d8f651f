@import './base.css';

/* 全局样式优化 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    '<PERSON>bunt<PERSON>', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Element Plus 样式覆盖 */
.el-button--primary {
  background: linear-gradient(135deg, #409EFF 0%, #5a9cff 100%);
  border: none;
  font-weight: 500;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #337ecc 0%, #4a8cff 100%);
}

.el-input__wrapper {
  border-radius: 8px !important;
  transition: all 0.2s ease;
}

.el-form-item__error {
  font-size: 12px;
  color: #f56c6c;
  margin-top: 4px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 移除冲突的样式，保持简洁 */
