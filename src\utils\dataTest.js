// 数据处理测试工具
export const testDataProcessing = () => {
  // 模拟API返回的数据
  const mockAPIResponse = {
    "history": [
      {
        "doc_name": "20250521_173757_7__2025-07-19T05-08-57_vqwo",
        "file_path": "C:\\Users\\<USER>\\Desktop\\demo\\flaskdemo\\api\\uploads\\20250521_173757_7__2025-07-19T05-08-57_vqwo\\20250521_1737577.jpg",
        "file_size": 842958,
        "filename": "20250521_1737577.jpg",
        "upload_time": "2025-07-19 13:09:03"
      },
      {
        "doc_name": "test_doc",
        "file_path": "C:\\Users\\<USER>\\Desktop\\demo\\flaskdemo\\api\\uploads\\test_doc\\test1.png",
        "file_size": 122276,
        "filename": "test1.png",
        "upload_time": "2025-07-19 11:58:48"
      }
    ],
    "page": 1,
    "pages": 1,
    "size": 20,
    "total": 2
  }

  console.log('🧪 开始数据处理测试...')
  console.log('📥 原始API响应:', mockAPIResponse)

  // 模拟数据处理逻辑
  if (mockAPIResponse && mockAPIResponse.history) {
    const processedData = mockAPIResponse.history.map(item => ({
      ...item,
      // 添加字段映射，保持向后兼容
      docName: item.doc_name,
      fileName: item.filename,
      fileSize: item.file_size,
      uploadTime: item.upload_time,
      status: 'success' // 默认状态
    }))

    console.log('✅ 处理后的数据:', processedData)
    console.log('📊 数据统计:', {
      原始数据长度: mockAPIResponse.history.length,
      处理后数据长度: processedData.length,
      总记录数: mockAPIResponse.total
    })

    // 验证每个字段
    if (processedData.length > 0) {
      const firstItem = processedData[0]
      console.log('🔍 第一条记录字段验证:')
      console.log('- doc_name:', firstItem.doc_name)
      console.log('- filename:', firstItem.filename)
      console.log('- file_size:', firstItem.file_size)
      console.log('- upload_time:', firstItem.upload_time)
      console.log('- status:', firstItem.status)
      console.log('- docName (映射):', firstItem.docName)
      console.log('- fileName (映射):', firstItem.fileName)
      console.log('- fileSize (映射):', firstItem.fileSize)
      console.log('- uploadTime (映射):', firstItem.uploadTime)
    }

    return {
      success: true,
      originalData: mockAPIResponse,
      processedData: processedData,
      total: mockAPIResponse.total
    }
  } else {
    console.log('❌ 数据处理失败: 没有找到history字段')
    return {
      success: false,
      error: '没有找到history字段'
    }
  }
}

// 测试时间格式化
export const testTimeFormatting = () => {
  const testTimes = [
    "2025-07-19 13:09:03",
    "2025-07-19 11:58:48",
    "2025-07-19T05:08:57",
    new Date().toISOString()
  ]

  console.log('🕒 时间格式化测试:')
  testTimes.forEach(time => {
    const formatted = formatTime(time)
    console.log(`${time} → ${formatted}`)
  })
}

// 格式化时间函数（复制自组件）
const formatTime = (timeString) => {
  if (!timeString) return ''
  // 处理不同的时间格式
  let date
  if (timeString.includes('T')) {
    // ISO格式: 2025-07-19T05:08:57
    date = new Date(timeString)
  } else if (timeString.includes('-') && timeString.includes(':')) {
    // 格式: 2025-07-19 13:09:03
    date = new Date(timeString.replace(' ', 'T'))
  } else {
    date = new Date(timeString)
  }

  return date.toLocaleString('zh-CN')
}

// 测试文件大小格式化
export const testFileSizeFormatting = () => {
  const testSizes = [842958, 122276, 0, 1024, 1048576, 1073741824]
  
  console.log('📁 文件大小格式化测试:')
  testSizes.forEach(size => {
    const formatted = formatFileSize(size)
    console.log(`${size} bytes → ${formatted}`)
  })
}

// 格式化文件大小函数（复制自组件）
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 运行所有测试
export const runAllDataTests = () => {
  console.log('🚀 开始运行所有数据测试...')
  
  const dataTest = testDataProcessing()
  testTimeFormatting()
  testFileSizeFormatting()
  
  console.log('✅ 所有数据测试完成')
  return dataTest
}

// 验证表格数据结构
export const validateTableData = (data) => {
  if (!Array.isArray(data)) {
    return { valid: false, error: '数据不是数组' }
  }

  if (data.length === 0) {
    return { valid: true, message: '数据为空数组' }
  }

  const requiredFields = ['doc_name', 'filename', 'file_size', 'upload_time']
  const missingFields = []
  
  const firstItem = data[0]
  requiredFields.forEach(field => {
    if (!(field in firstItem)) {
      missingFields.push(field)
    }
  })

  if (missingFields.length > 0) {
    return {
      valid: false,
      error: `缺少必需字段: ${missingFields.join(', ')}`,
      firstItem
    }
  }

  return {
    valid: true,
    message: `数据结构正确，包含 ${data.length} 条记录`,
    sample: firstItem
  }
}
