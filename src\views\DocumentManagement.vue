<template>
  <div class="document-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1>文档管理</h1>
          <p>管理系统中的所有文档，查看文档详情和统计信息</p>
        </div>
        <div class="header-actions">
          <el-button 
            type="primary" 
            :icon="Refresh" 
            @click="loadDocuments"
            :loading="loadingDocuments"
          >
            刷新列表
          </el-button>
          <el-button 
            type="success" 
            :icon="Plus" 
            @click="goToUpload"
          >
            上传新文档
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stat-card">
        <div class="stat-icon primary">
          <el-icon size="24"><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ documents.length }}</div>
          <div class="stat-label">总文档数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon success">
          <el-icon size="24"><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ processedCount }}</div>
          <div class="stat-label">可用文档</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon warning">
          <el-icon size="24"><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ documents.length }}</div>
          <div class="stat-label">文档总数</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <div class="filter-content">
        <el-input
          v-model="searchQuery"
          placeholder="搜索文档名称..."
          :prefix-icon="Search"
          clearable
          @input="handleSearch"
          class="search-input"
        />
        <el-select
          v-model="sortBy"
          placeholder="排序方式"
          @change="handleSort"
          class="sort-select"
        >
          <el-option label="按名称排序" value="name" />
          <el-option label="按ID排序" value="id" />
        </el-select>
        <el-select
          v-model="sortOrder"
          placeholder="排序顺序"
          @change="handleSort"
          class="sort-select"
        >
          <el-option label="升序" value="asc" />
          <el-option label="降序" value="desc" />
        </el-select>
      </div>
    </div>

    <!-- 文档列表 -->
    <div class="document-list-section">
      <!-- 加载状态 -->
      <div v-if="loadingDocuments" class="loading-container">
        <el-icon class="is-loading" size="32"><Loading /></el-icon>
        <p>正在加载文档列表...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="documentsError" class="error-container">
        <el-icon size="48" color="#f56c6c"><Warning /></el-icon>
        <h3>加载失败</h3>
        <p>{{ documentsError }}</p>
        <el-button type="primary" @click="loadDocuments">重新加载</el-button>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!filteredDocuments.length" class="empty-container">
        <el-icon size="64" color="#c0c4cc"><Document /></el-icon>
        <h3>{{ searchQuery ? '未找到匹配的文档' : '暂无文档' }}</h3>
        <p>{{ searchQuery ? '请尝试其他搜索关键词' : '开始上传您的第一个文档' }}</p>
        <el-button v-if="!searchQuery" type="primary" @click="goToUpload">
          上传文档
        </el-button>
      </div>

      <!-- 文档表格 -->
      <div v-else class="document-table">
        <el-table 
          :data="paginatedDocuments" 
          style="width: 100%"
          :default-sort="{ prop: sortBy, order: sortOrder === 'asc' ? 'ascending' : 'descending' }"
          @sort-change="handleTableSort"
        >
          <el-table-column prop="doc_name" label="文档名称" sortable min-width="200">
            <template #default="{ row }">
              <div class="document-name-cell">
                <el-icon class="document-icon"><Document /></el-icon>
                <span class="document-name">{{ row.doc_name || row.name || '未知文档' }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="id" label="文档ID" width="120" sortable>
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.id || 'N/A' }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="doc_type" label="文档类型" width="120">
            <template #default="{ row }">
              <el-tag size="small" type="info">
                <el-icon><Document /></el-icon>
                文档
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="created_at" label="创建时间" width="180" sortable>
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon><Clock /></el-icon>
                <span>{{ formatDate(row.created_at) || '未知' }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusType(row)" 
                size="small"
              >
                {{ getStatusText(row) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="viewDocument(row)"
                  :icon="View"
                >
                  查看
                </el-button>
                <el-button 
                  type="success" 
                  size="small" 
                  @click="downloadDocument(row)"
                  :icon="Download"
                >
                  下载
                </el-button>
                <el-dropdown @command="(command) => handleMoreActions(command, row)">
                  <el-button size="small" :icon="More">
                    更多
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="details">详细信息</el-dropdown-item>
                      <el-dropdown-item command="history">查看历史</el-dropdown-item>
                      <el-dropdown-item command="export">导出数据</el-dropdown-item>
                      <el-dropdown-item divided command="delete" class="danger-item">
                        删除文档
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredDocuments.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 文档详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="文档详情"
      width="600px"
      :before-close="handleCloseDetail"
    >
      <div v-if="selectedDocument" class="document-detail">
        <div class="detail-item">
          <label>文档名称：</label>
          <span>{{ selectedDocument.doc_name || selectedDocument.name }}</span>
        </div>
        <div class="detail-item">
          <label>文档ID：</label>
          <span>{{ selectedDocument.id || 'N/A' }}</span>
        </div>
        <div class="detail-item">
          <label>文档类型：</label>
          <el-tag size="small" type="info">
            <el-icon><Document /></el-icon>
            OCR文档
          </el-tag>
        </div>
        <div class="detail-item">
          <label>状态：</label>
          <el-tag :type="getStatusType(selectedDocument)">
            {{ getStatusText(selectedDocument) }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>操作：</label>
          <div class="detail-actions">
            <el-button size="small" type="primary" @click="viewDocument(selectedDocument)">
              查看历史记录
            </el-button>
            <el-button size="small" type="success" @click="downloadDocument(selectedDocument)">
              下载PDF
            </el-button>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="viewDocument(selectedDocument)">
            查看历史记录
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Folder,
  Clock,
  Check,
  Plus,
  Refresh,
  Search,
  Loading,
  Warning,
  View,
  Download,
  More
} from '@element-plus/icons-vue'
import { ocrAPI } from '../services/api'

const router = useRouter()

// 响应式数据
const documents = ref([])
const loadingDocuments = ref(false)
const documentsError = ref('')
const searchQuery = ref('')
const sortBy = ref('name')
const sortOrder = ref('desc')
const currentPage = ref(1)
const pageSize = ref(20)
const showDetailDialog = ref(false)
const selectedDocument = ref(null)

// 计算属性
const processedCount = computed(() => {
  // 根据实际数据结构，有doc_name和id的文档视为已处理
  return documents.value.filter(doc => doc.doc_name && doc.id).length
})

const recentCount = computed(() => {
  // 由于当前数据结构中没有created_at字段，暂时返回总数
  // 后续如果API提供了创建时间字段，可以恢复时间过滤逻辑
  return documents.value.length
})

const filteredDocuments = computed(() => {
  let filtered = [...documents.value]

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(doc =>
      (doc.doc_name || doc.name || '').toLowerCase().includes(query)
    )
  }

  // 排序
  filtered.sort((a, b) => {
    let aValue, bValue

    switch (sortBy.value) {
      case 'name':
        aValue = (a.doc_name || a.name || '').toLowerCase()
        bValue = (b.doc_name || b.name || '').toLowerCase()
        break
      case 'id':
        aValue = (a.id || '').toLowerCase()
        bValue = (b.id || '').toLowerCase()
        break
      default:
        return 0
    }

    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  return filtered
})

const paginatedDocuments = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredDocuments.value.slice(start, end)
})

// 生命周期
onMounted(() => {
  loadDocuments()
})

// 方法
const loadDocuments = async () => {
  loadingDocuments.value = true
  documentsError.value = ''

  try {
    console.log('📄 开始加载文档列表...')
    const response = await ocrAPI.getDocNames()

    if (response && response.docs && Array.isArray(response.docs)) {
      // 处理 { "docs": [...] } 格式
      documents.value = response.docs
      console.log('✅ 文档列表加载成功:', {
        数量: documents.value.length,
        文档: documents.value.map(doc => doc.doc_name || doc.name)
      })
      ElMessage.success(`成功加载 ${documents.value.length} 个文档`)
    } else if (response && response.data && Array.isArray(response.data)) {
      // 处理 { "data": [...] } 格式
      documents.value = response.data
      console.log('✅ 文档列表加载成功:', {
        数量: documents.value.length,
        文档: documents.value.map(doc => doc.doc_name || doc.name)
      })
      ElMessage.success(`成功加载 ${documents.value.length} 个文档`)
    } else if (response && Array.isArray(response)) {
      // 处理直接返回数组格式
      documents.value = response
      console.log('✅ 文档列表加载成功:', {
        数量: documents.value.length,
        文档: documents.value.map(doc => doc.doc_name || doc.name)
      })
      ElMessage.success(`成功加载 ${documents.value.length} 个文档`)
    } else {
      documents.value = []
      console.log('⚠️ API返回空数据或格式异常:', response)
      ElMessage.warning('未找到文档数据')
    }
  } catch (error) {
    console.error('❌ 加载文档列表失败:', error)
    documentsError.value = error.response?.data?.message || error.message || '加载文档列表失败'
    ElMessage.error(documentsError.value)
  } finally {
    loadingDocuments.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1 // 搜索时重置到第一页
}

const handleSort = () => {
  currentPage.value = 1 // 排序时重置到第一页
}

const handleTableSort = ({ prop, order }) => {
  sortBy.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

const goToUpload = () => {
  router.push('/ocr')
}

const viewDocument = (doc) => {
  console.log('📄 查看文档:', doc)
  router.push({
    path: '/history',
    query: { doc_name: doc.doc_name || doc.name }
  })
}

const downloadDocument = async (doc) => {
  try {
    console.log('📄 下载文档:', doc)
    ElMessage.info('开始下载文档...')

    const response = await ocrAPI.downloadPDF(doc.doc_name || doc.name)

    // 创建下载链接
    const blob = new Blob([response], { type: 'application/pdf' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${doc.doc_name || doc.name}.pdf`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('文档下载成功')
  } catch (error) {
    console.error('❌ 下载文档失败:', error)
    ElMessage.error('下载文档失败: ' + (error.response?.data?.message || error.message))
  }
}

const handleMoreActions = async (command, doc) => {
  switch (command) {
    case 'details':
      selectedDocument.value = doc
      showDetailDialog.value = true
      break
    case 'history':
      viewDocument(doc)
      break
    case 'export':
      ElMessage.info('导出功能开发中...')
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(
          `确定要删除文档 "${doc.doc_name || doc.name}" 吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        )
        ElMessage.info('删除功能开发中...')
      } catch {
        // 用户取消删除
      }
      break
  }
}

const handleCloseDetail = () => {
  showDetailDialog.value = false
  selectedDocument.value = null
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return dateString
  }
}

const getStatusType = (doc) => {
  // 根据文档ID和名称判断状态
  if (doc.doc_name && doc.id) {
    return 'success' // 有完整信息的文档视为已处理
  }
  return 'info' // 其他情况视为待处理
}

const getStatusText = (doc) => {
  // 根据文档ID和名称判断状态
  if (doc.doc_name && doc.id) {
    return '已创建' // 有完整信息的文档
  }
  return '待处理' // 其他情况
}
</script>

<style scoped>
.document-management {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.header-left p {
  color: #6b7280;
  margin: 0;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

/* 筛选区域 */
.filter-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-content {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 200px;
}

.sort-select {
  width: 150px;
}

/* 文档列表区域 */
.document-list-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 状态容器 */
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container p,
.error-container p,
.empty-container p {
  margin: 12px 0;
  color: #6b7280;
  font-size: 16px;
}

.error-container h3,
.empty-container h3 {
  margin: 16px 0 8px 0;
  color: #374151;
  font-size: 20px;
  font-weight: 500;
}

.error-container {
  color: #ef4444;
}

/* 表格样式 */
.document-table {
  margin-top: 20px;
}

.document-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.document-icon {
  color: #667eea;
  font-size: 16px;
}

.document-name {
  font-weight: 500;
  color: #1f2937;
}

.time-cell {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
}

.file-count-badge {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 分页 */
.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* 对话框 */
.document-detail {
  padding: 20px 0;
}

.detail-item {
  display: flex;
  margin-bottom: 16px;
  align-items: center;
}

.detail-item label {
  font-weight: 500;
  color: #374151;
  width: 100px;
  flex-shrink: 0;
}

.detail-item span {
  color: #6b7280;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 危险操作样式 */
:deep(.danger-item) {
  color: #ef4444 !important;
}

:deep(.danger-item:hover) {
  background-color: #fef2f2 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-management {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .filter-content {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input,
  .sort-select {
    width: 100%;
  }

  .stats-section {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons .el-button {
    width: 100%;
    margin: 0;
  }
}
</style>
