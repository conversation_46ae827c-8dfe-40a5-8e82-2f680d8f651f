<template>
  <div class="ocr-history-container">
    <div class="page-header">
      <h2>OCR识别历史</h2>
      <p>查看和管理您的文档识别记录</p>
      <!-- 调试信息 -->
      <div v-if="historyList.length > 0" class="debug-info">
        <el-tag type="info" size="small">
          数据状态: 已加载 {{ historyList.length }} 条记录
        </el-tag>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-number">{{ total }}</div>
                <div class="stats-label">总文档数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-icon success">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-number">{{ successCount }}</div>
                <div class="stats-label">识别成功</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-icon warning">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-number">{{ processingCount }}</div>
                <div class="stats-label">处理中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-icon danger">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stats-content">
                <div class="stats-number">{{ failedCount }}</div>
                <div class="stats-label">识别失败</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <el-form :model="filterForm" inline class="filter-form">
          <el-form-item label="文档名称">
            <el-input
              v-model="filterForm.docName"
              placeholder="搜索文档名称"
              :prefix-icon="Search"
              clearable
              @clear="handleSearch"
              @keyup.enter="handleSearch"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="文件名">
            <el-input
              v-model="filterForm.fileName"
              placeholder="搜索文件名"
              clearable
              @clear="handleSearch"
              @keyup.enter="handleSearch"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="上传时间">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilter">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 历史记录列表 -->
    <div class="history-section">
      <el-card class="history-card">
        <template #header>
          <div class="card-header">
            <span>识别记录 ({{ total }} 条)</span>
            <div class="header-actions">
              <el-button size="small" @click="testConnection" :loading="testingConnection">
                <el-icon><Connection /></el-icon>
                测试连接
              </el-button>
              <el-button size="small" @click="refreshList" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
              <el-button size="small" @click="showDebugInfo" v-if="historyList.length > 0">
                <el-icon><View /></el-icon>
                调试信息
              </el-button>
              <el-button size="small" @click="exportAll" :disabled="historyList.length === 0">
                <el-icon><Download /></el-icon>
                批量导出
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          v-loading="loading"
          :data="historyList"
          stripe
          class="history-table"
          @row-click="viewDetail"
          empty-text="暂无历史记录"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column prop="doc_name" label="文档信息" min-width="250">
            <template #default="{ row }">
              <div class="doc-name-cell">
                <div class="doc-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="doc-info">
                  <div class="doc-name" :title="row.doc_name">{{ row.doc_name }}</div>
                  <div class="file-name" :title="row.filename">
                    <el-icon><Paperclip /></el-icon>
                    {{ row.filename }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="upload_time" label="上传时间" width="160" sortable>
            <template #default="{ row }">
              <div class="time-cell">
                <el-icon><Clock /></el-icon>
                {{ formatTime(row.upload_time) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="file_size" label="文件大小" width="100" sortable>
            <template #default="{ row }">
              <div class="size-cell">
                {{ formatFileSize(row.file_size) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="ocrText" label="识别结果" min-width="200">
            <template #default="{ row }">
              <div class="ocr-preview">
                <div v-if="row.ocr_text" class="ocr-content">
                  <div class="ocr-text">{{ row.ocr_text.substring(0, 50) }}{{ row.ocr_text.length > 50 ? '...' : '' }}</div>
                  <div class="ocr-stats">
                    <el-tag size="small" type="success">{{ row.ocr_text.length }} 字符</el-tag>
                  </div>
                </div>
                <div v-else class="no-ocr">
                  <el-tag size="small" type="info">暂无识别结果</el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button size="small" type="primary" @click.stop="viewDetail(row)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-dropdown @command="(command) => handleActionCommand(command, row)" trigger="click">
                  <el-button size="small">
                    <el-icon><Download /></el-icon>
                    下载
                    <el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="preview-original" :disabled="!row.doc_name || !row.filename">
                        <el-icon><Picture /></el-icon>
                        预览原图
                      </el-dropdown-item>
                      <el-dropdown-item command="download-original" :disabled="!row.doc_name || !row.filename">
                        <el-icon><Document /></el-icon>
                        下载原图
                      </el-dropdown-item>
                      <el-dropdown-item command="download-pdf" :disabled="!row.doc_name">
                        <el-icon><DocumentCopy /></el-icon>
                        OCR PDF
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-button size="small" type="danger" @click.stop="deleteRecord(row)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      :title="`文档详情`"
      width="85%"
      top="3vh"
      class="detail-dialog"
      :close-on-click-modal="false"
    >
      <div v-if="currentRecord" class="detail-content">
        <!-- 文档头部信息 -->
        <div class="document-header">
          <div class="doc-icon-large">
            <el-icon><Document /></el-icon>
          </div>
          <div class="doc-main-info">
            <h3 class="doc-title">{{ currentRecord.doc_name }}</h3>
            <div class="doc-meta">
              <span class="meta-item">
                <el-icon><Paperclip /></el-icon>
                {{ currentRecord.filename }}
              </span>
              <span class="meta-item">
                <el-icon><Clock /></el-icon>
                {{ formatTime(currentRecord.upload_time) }}
              </span>
              <span class="meta-item">
                <el-icon><FolderOpened /></el-icon>
                {{ formatFileSize(currentRecord.file_size) }}
              </span>
            </div>
          </div>
          <div class="doc-status">
            <el-tag :type="getStatusType(currentRecord.status)" size="large">
              {{ getStatusText(currentRecord.status) }}
            </el-tag>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="detail-main">
          <!-- 左侧：图片预览 -->
          <div class="preview-section">
            <div class="section-header">
              <el-icon><Picture /></el-icon>
              <span>图片预览</span>
              <div class="header-actions">
                <el-button size="small" @click="downloadFile(currentRecord)" :disabled="!currentRecord?.doc_name || !currentRecord?.filename">
                  <el-icon><Download /></el-icon>
                  下载原图
                </el-button>
              </div>
            </div>
            <div class="image-container">
              <el-image
                :src="imagePreviewUrl"
                :preview-src-list="imagePreviewUrl ? [imagePreviewUrl] : []"
                fit="contain"
                class="preview-image"
                :loading="imageLoading"
                @load="handleImageLoad"
                @error="handleImageError"
              >
                <template #placeholder>
                  <div class="image-placeholder">
                    <el-icon><Loading /></el-icon>
                    <div>加载中...</div>
                  </div>
                </template>
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <div>图片加载失败</div>
                    <el-button size="small" @click="retryImageLoad">重试</el-button>
                  </div>
                </template>
              </el-image>
            </div>
            <div class="image-info">
              <div class="image-meta">
                <span v-if="currentRecord?.filename">
                  <el-icon><Document /></el-icon>
                  {{ currentRecord.filename }}
                </span>
                <span v-if="currentRecord?.file_size">
                  <el-icon><FolderOpened /></el-icon>
                  {{ formatFileSize(currentRecord.file_size) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 右侧：OCR结果和详细信息 -->
          <div class="info-section">
            <!-- OCR识别结果 -->
            <div class="ocr-section">
              <div class="section-header">
                <el-icon><Reading /></el-icon>
                <span>OCR识别结果</span>
                <el-tag v-if="currentRecord?.ocr_text" type="success" size="small" class="result-tag">
                  {{ currentRecord.ocr_text.length }} 字符
                </el-tag>
                <el-tag v-else type="info" size="small" class="result-tag">
                  暂无识别结果
                </el-tag>
              </div>

              <div v-if="loadingOCR" class="ocr-loading">
                <el-skeleton :rows="6" animated />
              </div>

              <div v-else-if="currentRecord?.ocr_text" class="ocr-result">
                <el-input
                  :value="currentRecord.ocr_text"
                  type="textarea"
                  :rows="10"
                  readonly
                  class="ocr-textarea"
                  placeholder="OCR识别结果将显示在这里..."
                />
                <div class="ocr-actions">
                  <el-button size="small" @click="copyResult(currentRecord.ocr_text)">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </el-button>
                  <el-button size="small" @click="downloadOCRResult(currentRecord, currentRecord.ocr_text)">
                    <el-icon><Download /></el-icon>
                    导出
                  </el-button>
                  <el-button size="small" type="primary" @click="performOCR(currentRecord)" :loading="loadingOCR">
                    <el-icon><Refresh /></el-icon>
                    刷新结果
                  </el-button>
                </div>
              </div>

              <div v-else class="no-ocr-result">
                <el-empty description="该文档暂无OCR识别结果" :image-size="80">
                  <el-button type="primary" @click="performOCR(currentRecord)" :loading="loadingOCR">
                    <el-icon><Refresh /></el-icon>
                    刷新数据
                  </el-button>
                </el-empty>
              </div>
            </div>

            <!-- 详细信息 -->
            <div class="detail-info-section">
              <div class="section-header">
                <el-icon><InfoFilled /></el-icon>
                <span>详细信息</span>
              </div>
              <div class="info-grid">
                <div class="info-item">
                  <label>文件路径</label>
                  <div class="path-display">
                    <el-input
                      :value="currentRecord.file_path"
                      readonly
                      size="small"
                    >
                      <template #append>
                        <el-button @click="copyPath(currentRecord.file_path)" size="small">
                          <el-icon><CopyDocument /></el-icon>
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                </div>
                <div class="info-item">
                  <label>创建时间</label>
                  <span>{{ formatTime(currentRecord.upload_time) }}</span>
                </div>
                <div class="info-item">
                  <label>文件大小</label>
                  <span>{{ formatFileSize(currentRecord.file_size) }}</span>
                </div>
                <div class="info-item">
                  <label>文件类型</label>
                  <span>{{ getFileType(currentRecord.filename) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-button @click="downloadFile(currentRecord)" :disabled="!currentRecord?.doc_name || !currentRecord?.filename">
              <el-icon><Download /></el-icon>
              下载原图
            </el-button>
            <el-button
              @click="downloadPDF(currentRecord)"
              :disabled="!currentRecord?.doc_name"
              :loading="downloadingPDF === currentRecord?.doc_name"
              type="success"
            >
              <el-icon><DocumentCopy /></el-icon>
              下载PDF
            </el-button>
            <el-button @click="openFileLocation(currentRecord)" :disabled="!currentRecord?.file_path">
              <el-icon><FolderOpened /></el-icon>
              打开位置
            </el-button>
          </div>
          <div class="footer-right">
            <el-button @click="closeDetailDialog">关闭</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Document,
  View,
  Download,
  Delete,
  CopyDocument,
  Clock,
  CircleCheck,
  CircleClose,
  Paperclip,
  InfoFilled,
  Reading,
  Picture,
  Connection,
  FolderOpened,
  Loading,
  ArrowDown,
  DocumentCopy
} from '@element-plus/icons-vue'
import { ocrAPI } from '../services/api'
import { getErrorMessage } from '../utils/apiTest'
import { runAllDataTests, validateTableData } from '../utils/dataTest'

// 响应式数据
const loading = ref(false)
const loadingOCR = ref(false)
const imageLoading = ref(true)
const testingConnection = ref(false)
const downloadingPDF = ref(null) // 当前正在下载PDF的文档名
const historyList = ref([])
const total = ref(0)
const detailVisible = ref(false)
const currentRecord = ref(null)
const imagePreviewUrl = ref('') // 图片预览URL

// 筛选表单
const filterForm = reactive({
  docName: '',
  fileName: '',
  dateRange: []
})

// 分页配置
const pagination = reactive({
  page: 1,
  size: 20
})

// 统计数据计算
const successCount = computed(() => {
  return historyList.value.filter(item => item.ocr_text && item.ocr_text.length > 0).length
})

const processingCount = computed(() => {
  return historyList.value.filter(item => item.status === 'processing' || item.status === 'pending').length
})

const failedCount = computed(() => {
  return historyList.value.filter(item => item.status === 'failed').length
})

// 页面加载时获取数据
onMounted(() => {
  console.log('🚀 OCRHistory页面已挂载，开始加载数据...')
  loadHistoryList()
})



// 加载历史记录列表
const loadHistoryList = async () => {
  try {
    loading.value = true

    const params = {
      page: pagination.page,
      size: pagination.size
    }

    // 添加搜索条件
    if (filterForm.docName) {
      params.doc_name = filterForm.docName
    }
    if (filterForm.fileName) {
      params.filename = filterForm.fileName
    }
    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      params.start_date = filterForm.dateRange[0]
      params.end_date = filterForm.dateRange[1]
    }

    const response = await ocrAPI.getHistory(params)
    console.log('� 历史记录API响应:', response)
    console.log('📋 响应类型:', typeof response)
    console.log('📋 是否有history字段:', !!response?.history)
    console.log('📋 history数组长度:', response?.history?.length)

    if (response && response.history) {
      // 映射API字段到组件使用的字段
      historyList.value = response.history.map(item => ({
        ...item,
        // 添加字段映射，保持向后兼容
        docName: item.doc_name,
        fileName: item.filename,
        fileSize: item.file_size,
        uploadTime: item.upload_time,
        // 根据是否有OCR结果来设置状态
        status: item.ocr_text ? 'success' : 'pending',
        // 添加OCR文本内容
        ocrText: item.ocr_text || '',
        textLength: item.ocr_text ? item.ocr_text.length : 0
      }))
      total.value = response.total || response.history.length

      console.log('✅ 数据处理完成:', {
        原始数据: response.history.length,
        处理后数据: historyList.value.length,
        第一条记录: historyList.value[0],
        原始第一条: response.history[0]
      })

      // 验证数据字段
      if (historyList.value.length > 0) {
        const firstItem = historyList.value[0]
        console.log('🔍 字段验证:', {
          'doc_name': firstItem.doc_name,
          'filename': firstItem.filename,
          'file_size': firstItem.file_size,
          'upload_time': firstItem.upload_time,
          'ocr_text': firstItem.ocr_text ? `${firstItem.ocr_text.substring(0, 50)}...` : '无',
          'status': firstItem.status,
          'textLength': firstItem.textLength
        })
      }

      ElMessage.success(`加载了 ${response.history.length} 条历史记录`)
    } else {
      historyList.value = []
      total.value = 0
      ElMessage.info('暂无历史记录')
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)

    if (error.code === 'ERR_NETWORK') {
      ElMessage.error('无法连接到服务器，请检查网络连接')
    } else if (error.response?.status === 401) {
      ElMessage.error('未授权访问，请重新登录')
    } else if (error.response?.status === 404) {
      ElMessage.error('API接口不存在')
    } else {
      ElMessage.error('获取历史记录失败')
    }

    historyList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}



// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadHistoryList()
}

// 重置筛选
const resetFilter = () => {
  filterForm.docName = ''
  filterForm.fileName = ''
  filterForm.dateRange = []
  handleSearch()
}

// 刷新列表
const refreshList = () => {
  loadHistoryList()
}

// 显示调试信息
const showDebugInfo = () => {
  console.log('🔍 当前数据状态:')
  console.log('- historyList长度:', historyList.value.length)
  console.log('- total值:', total.value)
  console.log('- 第一条记录:', historyList.value[0])
  console.log('- 完整数据:', historyList.value)

  // 运行数据测试
  console.log('\n🧪 运行数据处理测试:')
  runAllDataTests()

  // 验证当前表格数据
  console.log('\n✅ 验证当前表格数据:')
  const validation = validateTableData(historyList.value)
  console.log('验证结果:', validation)

  ElMessage.info(`当前有 ${historyList.value.length} 条记录，详细信息已输出到控制台`)
}

// 测试API连接
const testConnection = async () => {
  testingConnection.value = true
  try {
    console.log('🔍 开始测试API连接...')
    const response = await ocrAPI.getHistory({ page: 1, size: 1 })

    if (response) {
      ElMessage.success('✅ API连接正常')
      console.log('✅ API测试成功:', response)

      // 显示详细信息
      if (response.history && Array.isArray(response.history)) {
        console.log(`📊 返回数据: ${response.history.length} 条记录，总计 ${response.total} 条`)
      }
    } else {
      ElMessage.warning('⚠️ API连接异常，返回数据为空')
      console.log('⚠️ API返回空数据')
    }
  } catch (error) {
    console.error('❌ API连接测试失败:', error)

    const errorMessage = getErrorMessage(error)
    ElMessage.error(`❌ ${errorMessage}`)

    // 提供详细的调试信息
    console.log('🔧 调试信息:')
    console.log('- 请求URL:', error.config?.url || '/api/ocr/history')
    console.log('- 错误类型:', error.code || 'UNKNOWN')
    console.log('- 状态码:', error.response?.status || 'N/A')
    console.log('- 错误详情:', error.message)

    // 提供解决建议
    if (error.code === 'ERR_NETWORK') {
      console.log('💡 解决建议:')
      console.log('1. 确保后端服务正在运行: python app.py')
      console.log('2. 检查后端服务端口是否为5000')
      console.log('3. 检查防火墙设置')
      console.log('4. 尝试重启开发服务器: npm run dev')
    }
  } finally {
    testingConnection.value = false
  }
}

// 批量导出
const exportAll = () => {
  if (historyList.value.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  const csvContent = generateCSVContent(historyList.value)
  downloadCSV(csvContent, `OCR历史记录_${new Date().toISOString().split('T')[0]}.csv`)
  ElMessage.success('导出成功')
}

// 生成CSV内容
const generateCSVContent = (data) => {
  const headers = ['文档名称', '文件名', '上传时间', '文件大小', '状态', '识别字符数', 'OCR识别结果']
  const rows = data.map(item => [
    item.doc_name,
    item.filename,
    formatTime(item.upload_time),
    formatFileSize(item.file_size),
    getStatusText(item.status),
    item.ocr_text ? item.ocr_text.length : 0,
    item.ocr_text ? item.ocr_text.replace(/"/g, '""') : '暂无识别结果'
  ])

  return [headers, ...rows].map(row =>
    row.map(cell => `"${cell}"`).join(',')
  ).join('\n')
}

// 下载CSV文件
const downloadCSV = (content, filename) => {
  const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadHistoryList()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadHistoryList()
}

// 查看详情
const viewDetail = async (row) => {
  currentRecord.value = { ...row }
  detailVisible.value = true
  imageLoading.value = true
  loadingOCR.value = true

  console.log('📋 查看文档详情:', {
    文档名称: row.doc_name,
    文件名: row.filename,
    文件大小: formatFileSize(row.file_size),
    上传时间: formatTime(row.upload_time),
    OCR结果: row.ocr_text ? `${row.ocr_text.length} 字符` : '无'
  })

  try {
    // 使用新的API获取详细信息
    const detailResponse = await ocrAPI.getHistoryDetail(row.doc_name, row.filename)

    if (detailResponse && detailResponse.data) {
      // 更新当前记录为详细信息
      currentRecord.value = { ...currentRecord.value, ...detailResponse.data }
      console.log('✅ 获取详细信息成功:', {
        文档名称: detailResponse.data.doc_name,
        OCR结果长度: detailResponse.data.ocr_text ? detailResponse.data.ocr_text.length : 0
      })
    } else {
      console.log('⚠️ 使用列表中的基本信息')
    }
  } catch (error) {
    console.error('❌ 获取详细信息失败:', error)
    ElMessage.warning('获取详细信息失败，显示基本信息')
    // 如果获取详细信息失败，继续使用列表中的基本信息
  } finally {
    loadingOCR.value = false
  }

  // 加载图片预览
  await loadImagePreview(currentRecord.value)
}

// 下载原图文件
const downloadFile = async (row) => {
  if (!row?.doc_name || !row?.filename) {
    ElMessage.warning('文档信息不完整，无法下载')
    return
  }

  try {
    console.log('📄 开始下载原图:', { doc_name: row.doc_name, filename: row.filename })
    console.log('📄 原始文件名:', row.filename)
    console.log('📄 完整行数据:', row)
    ElMessage.info('正在下载原图文件...')

    const response = await ocrAPI.downloadOriginalImage(row.doc_name, row.filename)

    // 创建下载链接
    const mimeType = getImageMimeType(row.filename)
    console.log('📄 检测到的MIME类型:', mimeType)
    console.log('📄 文件扩展名分析:', {
      原始文件名: row.filename,
      提取的扩展名: row.filename.split('.').pop()?.toLowerCase(),
      映射的MIME类型: mimeType
    })

    // 检查MIME类型和文件扩展名是否匹配
    const actualExt = row.filename.split('.').pop()?.toLowerCase()
    const expectedMimeType = actualExt === 'jpg' || actualExt === 'jpeg' ? 'image/jpeg' :
                            actualExt === 'png' ? 'image/png' :
                            actualExt === 'gif' ? 'image/gif' : 'application/octet-stream'

    if (mimeType !== expectedMimeType) {
      console.warn('⚠️ MIME类型不匹配:', {
        文件扩展名: actualExt,
        预期MIME: expectedMimeType,
        实际MIME: mimeType
      })
    }

    // 对于下载，使用通用的二进制类型，避免浏览器自动修改扩展名
    // 这样可以保持原始文件名不变
    const blob = new Blob([response], { type: 'application/octet-stream' })
    console.log('📄 使用通用MIME类型避免扩展名被修改')
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = row.filename
    console.log('📄 设置的下载文件名:', link.download)
    document.body.appendChild(link)

    // 在点击前再次检查下载属性
    console.log('📄 点击下载前的最终检查:')
    console.log('- link.download:', link.download)
    console.log('- link.href:', link.href)
    console.log('- blob.type:', blob.type)
    console.log('- blob.size:', blob.size)

    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('原图文件下载成功')
    console.log('✅ 下载完成，文件名应为:', row.filename)
  } catch (error) {
    console.error('❌ 下载原图失败:', error)
    ElMessage.error('下载原图失败: ' + (error.response?.data?.message || error.message))
  }
}

// 下载PDF文档
const downloadPDF = async (row) => {
  if (!row?.doc_name) {
    ElMessage.warning('无法获取文档信息')
    return
  }

  downloadingPDF.value = row.doc_name

  try {
    console.log('📄 开始下载PDF:', row.doc_name)
    console.log('📄 完整行对象:', row)
    ElMessage.info('正在生成PDF文档，请稍候...')

    const response = await ocrAPI.downloadPDF(row.doc_name)

    // 创建下载链接
    const blob = new Blob([response], { type: 'application/pdf' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名
    const fileName = `${row.doc_name}_ocr.pdf`
    link.download = fileName

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    window.URL.revokeObjectURL(url)

    ElMessage.success('PDF文档下载成功')
    console.log('✅ PDF下载完成:', fileName)

  } catch (error) {
    console.error('PDF下载失败:', error)

    let errorMessage = 'PDF下载失败'
    if (error.response?.status === 404) {
      errorMessage = '文档不存在或PDF尚未生成'
    } else if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    downloadingPDF.value = null
  }
}

// 处理操作命令
const handleActionCommand = (command, row) => {
  switch (command) {
    case 'preview-original':
      previewOriginalImage(row)
      break
    case 'download-original':
      downloadFile(row)
      break
    case 'download-pdf':
      downloadPDF(row)
      break
    default:
      console.warn('未知的操作命令:', command)
  }
}

// 预览原图
const previewOriginalImage = async (row) => {
  if (!row?.doc_name || !row?.filename) {
    ElMessage.warning('文档信息不完整，无法预览')
    return
  }

  // 打开详情对话框并显示图片
  currentRecord.value = row
  detailVisible.value = true
  imageLoading.value = true

  ElMessage.info('正在加载图片预览...')

  // 加载图片预览
  await loadImagePreview(row)
}

// 加载图片预览
const loadImagePreview = async (record) => {
  if (!record?.doc_name || !record?.filename) {
    imagePreviewUrl.value = ''
    imageLoading.value = false
    return
  }

  try {
    console.log('📸 开始加载图片预览:', { doc_name: record.doc_name, filename: record.filename })

    // 使用下载API获取图片数据
    const response = await ocrAPI.downloadOriginalImage(record.doc_name, record.filename)

    // 创建blob URL
    const mimeType = getImageMimeType(record.filename)
    const blob = new Blob([response], { type: mimeType })
    const url = URL.createObjectURL(blob)

    // 清理之前的URL
    if (imagePreviewUrl.value) {
      URL.revokeObjectURL(imagePreviewUrl.value)
    }

    imagePreviewUrl.value = url
    console.log('✅ 图片预览加载成功')
  } catch (error) {
    console.error('❌ 加载图片预览失败:', error)
    imagePreviewUrl.value = ''
    ElMessage.warning('图片预览加载失败')
  } finally {
    imageLoading.value = false
  }
}

// 清理图片预览URL
const cleanupImagePreview = () => {
  if (imagePreviewUrl.value) {
    URL.revokeObjectURL(imagePreviewUrl.value)
    imagePreviewUrl.value = ''
  }
}

// 关闭详情对话框
const closeDetailDialog = () => {
  cleanupImagePreview()
  detailVisible.value = false
  currentRecord.value = null
}

// 处理图片加载成功
const handleImageLoad = () => {
  imageLoading.value = false
  console.log('✅ 图片显示成功')
}

// 处理图片加载错误
const handleImageError = () => {
  imageLoading.value = false
  console.error('❌ 图片显示失败')
  ElMessage.warning('图片显示失败')
}

// 重试加载图片
const retryImageLoad = async () => {
  if (!currentRecord.value) return

  imageLoading.value = true
  await loadImagePreview(currentRecord.value)
}

// 获取图片MIME类型
const getImageMimeType = (filename) => {
  if (!filename) return 'application/octet-stream'
  const ext = filename.split('.').pop()?.toLowerCase()
  const mimeTypes = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'webp': 'image/webp',
    'svg': 'image/svg+xml',
    'tiff': 'image/tiff',
    'tif': 'image/tiff'
  }
  return mimeTypes[ext] || 'application/octet-stream'
}

// 获取文件类型
const getFileType = (filename) => {
  if (!filename) return '未知'
  const ext = filename.split('.').pop()?.toLowerCase()
  const types = {
    'jpg': 'JPEG图片',
    'jpeg': 'JPEG图片',
    'png': 'PNG图片',
    'gif': 'GIF图片',
    'bmp': 'BMP图片',
    'webp': 'WebP图片',
    'tiff': 'TIFF图片',
    'pdf': 'PDF文档'
  }
  return types[ext] || `${ext?.toUpperCase() || '未知'}文件`
}

// 打开文件位置
const openFileLocation = (record) => {
  if (!record?.file_path) {
    ElMessage.warning('文件路径不存在')
    return
  }

  // 在实际应用中，这里可能需要调用系统API来打开文件夹
  ElMessage.info('文件位置功能需要系统支持')
  console.log('文件路径:', record.file_path)
}



// 重新加载OCR结果（刷新数据）
const performOCR = async (record) => {
  try {
    loadingOCR.value = true
    ElMessage.info('正在重新获取OCR识别结果...')

    // 使用新的API获取该文档的最新详细信息
    const response = await ocrAPI.getHistoryDetail(record.doc_name, record.filename)

    if (response && response.data) {
      // 更新当前记录，包含最新的OCR结果
      currentRecord.value = { ...currentRecord.value, ...response.data }

      if (response.data.ocr_text) {
        ElMessage.success('OCR结果已更新')
        console.log('✅ OCR结果已更新:', {
          文档: response.data.doc_name,
          字符数: response.data.ocr_text.length,
          内容预览: response.data.ocr_text.substring(0, 100) + '...'
        })
      } else {
        ElMessage.warning('该文档暂无OCR识别结果')
      }
    } else {
      ElMessage.warning('未找到文档数据')
    }
  } catch (error) {
    console.error('获取OCR结果失败:', error)
    if (error.response?.status === 404) {
      ElMessage.error('文档不存在')
    } else {
      ElMessage.error('获取OCR结果失败')
    }
  } finally {
    loadingOCR.value = false
  }
}

// 删除记录
const deleteRecord = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文档"${row.doc_name}"的识别记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 实际API调用（当后端可用时取消注释）
    // await ocrAPI.deleteHistory(row.id)

    ElMessage.success('删除成功')
    loadHistoryList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 复制结果
const copyResult = async (content) => {
  if (!content) {
    ElMessage.warning('没有可复制的内容')
    return
  }

  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('文本已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动选择文本复制')
  }
}

// 复制路径
const copyPath = async (path) => {
  if (!path) {
    ElMessage.warning('路径为空')
    return
  }

  try {
    await navigator.clipboard.writeText(path)
    ElMessage.success('路径已复制到剪贴板')
  } catch (error) {
    console.error('复制路径失败:', error)
    ElMessage.error('复制失败')
  }
}

// 下载OCR结果
const downloadOCRResult = (record, content) => {
  if (!content) {
    ElMessage.warning('没有可导出的内容')
    return
  }

  const textContent = `文档名称: ${record.doc_name}\n文件名: ${record.filename}\n上传时间: ${formatTime(record.upload_time)}\n文件大小: ${formatFileSize(record.file_size)}\n\n识别内容:\n${content}`

  const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)

  const link = document.createElement('a')
  link.href = url
  link.download = `${record.doc_name}_OCR识别结果.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  ElMessage.success('OCR结果导出成功')
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    success: 'success',
    failed: 'danger',
    processing: 'warning',
    pending: 'info'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    success: '已识别',
    failed: '识别失败',
    processing: '识别中',
    pending: '待识别'
  }
  return texts[status] || '未知'
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  // 处理不同的时间格式
  let date
  if (timeString.includes('T')) {
    // ISO格式: 2025-07-19T05:08:57
    date = new Date(timeString)
  } else if (timeString.includes('-') && timeString.includes(':')) {
    // 格式: 2025-07-19 13:09:03
    date = new Date(timeString.replace(' ', 'T'))
  } else {
    date = new Date(timeString)
  }

  return date.toLocaleString('zh-CN')
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.ocr-history-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-header h2 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 24px;
}

.stats-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 20px;
}

.stats-icon.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stats-icon.danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.filter-form {
  margin: 0;
}

/* 历史记录区域样式 */
.history-section {
  margin-bottom: 24px;
}

.history-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #1f2937;
  font-size: 18px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.history-table {
  margin-bottom: 16px;
}

.doc-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.doc-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.doc-info {
  flex: 1;
  min-width: 0;
}

.doc-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-name {
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.time-cell {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
}

.size-cell {
  font-weight: 500;
  color: #374151;
}

/* OCR预览样式 */
.ocr-preview {
  max-width: 200px;
}

.ocr-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ocr-text {
  font-size: 13px;
  color: #374151;
  line-height: 1.4;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.ocr-stats {
  display: flex;
  justify-content: flex-start;
}

.no-ocr {
  display: flex;
  align-items: center;
  color: #9ca3af;
  font-style: italic;
}

.pagination-section {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

/* 详情对话框样式 */
.detail-dialog :deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
}

.detail-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
}

.detail-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.detail-content {
  padding: 0;
  max-height: 75vh;
  overflow-y: auto;
}

/* 文档头部信息 */
.document-header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e5e7eb;
}

.doc-icon-large {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  flex-shrink: 0;
}

.doc-main-info {
  flex: 1;
  min-width: 0;
}

.doc-title {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  word-break: break-all;
}

.doc-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
}

.doc-status {
  flex-shrink: 0;
}

/* 主要内容区域 */
.detail-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  padding: 24px;
  min-height: 500px;
}


/* 左侧预览区域 */

.preview-section {
  
  display: flex;
  
  flex-direction: column;
  
  gap: 16px;
  
}


.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-weight: 600;
  color: #1f2937;
  font-size: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.section-header .header-actions {
  display: flex;
  gap: 8px;
}

.image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
  min-height: 400px;
  position: relative;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-placeholder,
.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #9ca3af;
  font-size: 14px;
}

.image-error {
  padding: 40px;
}

.image-info {
  padding: 12px 0;
  border-top: 1px solid #e5e7eb;
  margin-top: 12px;
}

.image-meta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #6b7280;
}

.image-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 右侧信息区域 */
.info-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* OCR结果区域 */
.ocr-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-tag {
  margin-left: auto;
}

.ocr-loading {
  padding: 20px;
}

.ocr-result {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.ocr-textarea :deep(.el-textarea__inner) {
  font-family: 'Courier New', monospace;
  line-height: 1.6;
  font-size: 14px;
  resize: none;
}

.ocr-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.no-ocr-result {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 详细信息区域 */
.detail-info-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
}

.info-grid {
  display: grid;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.info-item span {
  color: #6b7280;
  font-size: 14px;
}

.path-display :deep(.el-input-group__append) {
  padding: 0 8px;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.footer-left,
.footer-right {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .detail-main {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .preview-section {
    order: 2;
  }

  .info-section {
    order: 1;
  }

  .image-container {
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .ocr-history-container {
    padding: 16px;
  }

  .stats-section :deep(.el-col) {
    margin-bottom: 16px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .detail-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 2vh auto;
  }

  .detail-content {
    max-height: 80vh;
  }

  .document-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .doc-meta {
    justify-content: center;
  }

  .detail-main {
    padding: 16px;
    gap: 16px;
  }

  .image-container {
    min-height: 250px;
  }
}

@media (max-width: 480px) {
  .page-header h2 {
    font-size: 24px;
  }

  .stats-number {
    font-size: 20px;
  }

  .doc-name-cell {
    gap: 8px;
  }

  .doc-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .doc-icon-large {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .doc-title {
    font-size: 18px;
  }

  .doc-meta {
    flex-direction: column;
    gap: 8px;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 12px;
  }

  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }

  .footer-left .el-button,
  .footer-right .el-button {
    flex: 1;
  }

  .ocr-actions {
    flex-direction: column;
  }

  .ocr-actions .el-button {
    width: 100%;
  }
}
</style>
