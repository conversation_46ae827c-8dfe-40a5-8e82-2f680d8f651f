// Session管理工具
import { ElMessage } from 'element-plus'

/**
 * 获取Cookie值
 */
export const getCookie = (name) => {
  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  if (parts.length === 2) {
    return parts.pop().split(';').shift()
  }
  return null
}

/**
 * 设置Cookie
 */
export const setCookie = (name, value, days = 7) => {
  const expires = new Date()
  expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`
}

/**
 * 删除Cookie
 */
export const deleteCookie = (name) => {
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
}

/**
 * 检查是否有有效的session
 */
export const hasValidSession = () => {
  const sessionCookie = getCookie('session')
  const token = localStorage.getItem('token')
  return !!(sessionCookie || token)
}

/**
 * 获取session信息
 */
export const getSessionInfo = () => {
  return {
    sessionCookie: getCookie('session'),
    token: localStorage.getItem('token'),
    user: JSON.parse(localStorage.getItem('user') || '{}')
  }
}

/**
 * 清除所有session信息
 */
export const clearSession = () => {
  deleteCookie('session')
  localStorage.removeItem('token')
  localStorage.removeItem('user')
}

/**
 * 检查API响应中的session状态
 */
export const checkSessionFromResponse = (error) => {
  if (error.response?.status === 401) {
    ElMessage.error('登录已过期，请重新登录')
    clearSession()
    // 跳转到登录页
    window.location.href = '/'
    return true
  }
  return false
}

/**
 * 模拟登录后设置session（用于测试）
 */
export const mockLogin = (username) => {
  const mockSessionId = `mock_session_${Date.now()}_${Math.random().toString(36).substring(2)}`
  setCookie('session', mockSessionId)
  
  const userData = {
    username: username,
    loginTime: new Date().toISOString()
  }
  localStorage.setItem('user', JSON.stringify(userData))
  localStorage.setItem('token', mockSessionId)
  
  return {
    sessionId: mockSessionId,
    user: userData
  }
}

/**
 * 验证session有效性
 */
export const validateSession = async () => {
  try {
    // 这里可以调用一个验证session的API
    // const response = await api.get('/auth/validate')
    // return response.valid
    
    // 目前简单检查是否存在session信息
    return hasValidSession()
  } catch (error) {
    console.error('Session验证失败:', error)
    return false
  }
}

/**
 * 获取请求头中需要的认证信息
 */
export const getAuthHeaders = () => {
  const headers = {}
  
  const token = localStorage.getItem('token')
  if (token) {
    headers.Authorization = `Bearer ${token}`
  }
  
  // session cookie会自动发送，不需要手动设置
  return headers
}

/**
 * 处理登录成功后的session设置
 */
export const handleLoginSuccess = (response, credentials) => {
  // 如果后端返回了session信息
  if (response.sessionId) {
    setCookie('session', response.sessionId)
  }
  
  // 保存token
  if (response.token) {
    localStorage.setItem('token', response.token)
  }
  
  // 保存用户信息
  const userData = response.user || { username: credentials.username }
  localStorage.setItem('user', JSON.stringify(userData))
  
  return userData
}

/**
 * 检查是否需要重新登录
 */
export const shouldRelogin = () => {
  const lastLoginTime = localStorage.getItem('lastLoginTime')
  if (!lastLoginTime) return true
  
  const now = Date.now()
  const loginTime = parseInt(lastLoginTime)
  const sessionTimeout = 24 * 60 * 60 * 1000 // 24小时
  
  return (now - loginTime) > sessionTimeout
}

/**
 * 更新最后登录时间
 */
export const updateLastLoginTime = () => {
  localStorage.setItem('lastLoginTime', Date.now().toString())
}

/**
 * 获取session状态信息（用于调试）
 */
export const getSessionStatus = () => {
  return {
    hasSession: hasValidSession(),
    sessionCookie: getCookie('session'),
    token: localStorage.getItem('token'),
    user: localStorage.getItem('user'),
    lastLoginTime: localStorage.getItem('lastLoginTime'),
    shouldRelogin: shouldRelogin()
  }
}
