<template>
  <div class="ocr-upload-container">
    <div class="page-header">
      <h2>OCR文档识别</h2>
      <p>上传图片文件，系统将自动识别其中的文字内容</p>
    </div>

    <div class="upload-section">
      <el-card class="upload-card">
        <template #header>
          <div class="card-header">
            <el-icon><Upload /></el-icon>
            <span>上传文档</span>
          </div>
        </template>

        <!-- 文档名称输入 -->
        <div class="doc-name-section">
          <el-form :model="uploadForm" :rules="uploadRules" ref="uploadFormRef">
            <el-form-item label="文档名称" prop="docName" required>
              <el-input
                v-model="uploadForm.docName"
                placeholder="请输入文档名称，用于标识和管理"
                size="large"
                :prefix-icon="Document"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 文件上传区域 -->
        <div class="upload-area">
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            drag
            :auto-upload="false"
            :before-upload="beforeUpload"
            :show-file-list="false"
            :disabled="uploading"
            accept="image/*,.pdf"
            @change="handleFileChange"
          >
            <div class="upload-content">
              <el-icon class="upload-icon" size="48" color="#409EFF">
                <Plus />
              </el-icon>
              <div class="upload-text">
                <p class="upload-title">点击或拖拽文件到此区域上传</p>
                <p class="upload-hint">支持 JPG、PNG、PDF 等格式，文件大小不超过 10MB</p>
                <p class="upload-time-hint">⏱️ OCR识别通常需要1-2分钟，请耐心等待</p>
              </div>
            </div>
          </el-upload>

          <!-- 上传进度 -->
          <div v-if="uploading" class="upload-progress">
            <el-progress
              :percentage="uploadProgress"
              :status="uploadProgress === 100 ? 'success' : ''"
              :stroke-width="8"
            />
            <p class="progress-text">{{ uploadProgressText }}</p>
            <div class="progress-details">
              <div class="progress-info">
                <el-icon class="loading-icon"><Loading /></el-icon>
                <span>OCR识别通常需要1-2分钟，请耐心等待...</span>
              </div>
              <div class="progress-time">
                <span>已用时: {{ formatElapsedTime(elapsedTime) }}</span>
              </div>
            </div>
          </div>

          <!-- 上传按钮 -->
          <div v-if="selectedFile && !uploading" class="upload-actions">
            <el-button type="primary" size="large" @click="handleUpload" :loading="uploading">
              <el-icon><Upload /></el-icon>
              开始识别
            </el-button>
            <el-button size="large" @click="clearFile">
              取消
            </el-button>
          </div>
        </div>

        <!-- 预览区域 -->
        <div v-if="previewImage" class="preview-section">
          <h4>文件预览</h4>
          <div class="preview-container">
            <img :src="previewImage" alt="预览图片" class="preview-image" />
            <div class="preview-info">
              <p><strong>文件名:</strong> {{ selectedFile?.name }}</p>
              <p><strong>文件大小:</strong> {{ formatFileSize(selectedFile?.size) }}</p>
              <p><strong>文档名称:</strong> {{ uploadForm.docName }}</p>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- OCR结果展示 -->
    <div v-if="ocrResult" class="result-section">
      <el-card class="result-card">
        <template #header>
          <div class="card-header">
            <el-icon><DocumentChecked /></el-icon>
            <span>识别结果</span>
            <div class="header-actions">
              <el-button size="small" @click="copyResult">
                <el-icon><CopyDocument /></el-icon>
                复制文本
              </el-button>
              <el-button size="small" type="primary" @click="downloadResult">
                <el-icon><Download /></el-icon>
                导出文档
              </el-button>
            </div>
          </div>
        </template>

        <div class="result-content">
          <div class="result-info">
            <el-descriptions :column="3" border>
              <el-descriptions-item label="文档名称">{{ ocrResult.docName }}</el-descriptions-item>
              <el-descriptions-item label="识别时间">{{ formatTime(ocrResult.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="识别状态">
                <el-tag type="success">识别完成</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="文字数量">{{ ocrResult.textContent?.length || 0 }} 字符</el-descriptions-item>
              <el-descriptions-item label="置信度">{{ ocrResult.confidence || '95%' }}</el-descriptions-item>
              <el-descriptions-item label="处理时间">{{ ocrResult.processTime || '2.3s' }}</el-descriptions-item>
            </el-descriptions>
          </div>

          <div class="result-text">
            <h4>识别文本内容</h4>
            <el-input
              v-model="ocrResult.textContent"
              type="textarea"
              :rows="12"
              placeholder="识别的文本内容将显示在这里..."
              class="result-textarea"
            />
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Upload,
  Plus,
  Document,
  DocumentChecked,
  CopyDocument,
  Download,
  Loading,
  Connection
} from '@element-plus/icons-vue'
import { ocrAPI } from '../services/api'
import { validateFile, getErrorMessage, generateDocName, testOCRConnection, formatOCRResult } from '../utils/apiTest'

// 响应式数据
const uploadFormRef = ref()
const uploadRef = ref()
const uploading = ref(false)
const uploadProgress = ref(0)
const previewImage = ref('')
const selectedFile = ref(null)
const ocrResult = ref(null)
const elapsedTime = ref(0)
const startTime = ref(null)
const testing = ref(false)

// 表单数据
const uploadForm = reactive({
  docName: ''
})

// 表单验证规则
const uploadRules = {
  docName: [
    { required: true, message: '请输入文档名称', trigger: 'blur' },
    { min: 2, max: 50, message: '文档名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 上传进度文本
const uploadProgressText = computed(() => {
  if (uploadProgress.value < 10) {
    return '正在上传文件...'
  } else if (uploadProgress.value < 20) {
    return '文件上传完成，开始OCR识别...'
  } else if (uploadProgress.value < 40) {
    return '正在分析图片内容...'
  } else if (uploadProgress.value < 60) {
    return '正在识别文字信息...'
  } else if (uploadProgress.value < 80) {
    return '正在优化识别结果...'
  } else if (uploadProgress.value < 95) {
    return '正在生成最终结果...'
  } else {
    return '处理完成，请稍候...'
  }
})

// 文件选择处理
const handleFileChange = (uploadFile) => {
  const file = uploadFile.raw

  // 使用工具函数验证文件
  const validation = validateFile(file)
  if (!validation.valid) {
    validation.errors.forEach(error => {
      ElMessage.error(error)
    })
    return false
  }

  // 保存文件信息并生成预览
  selectedFile.value = file

  // 如果用户没有输入文档名称，自动生成一个
  if (!uploadForm.docName.trim()) {
    uploadForm.docName = generateDocName(file.name)
  }

  // 生成图片预览
  if (file.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      previewImage.value = e.target.result
    }
    reader.readAsDataURL(file)
  } else {
    // PDF文件显示文件图标
    previewImage.value = ''
  }

  // 清除之前的结果
  ocrResult.value = null
}

// 文件上传前的检查（保留用于兼容性）
const beforeUpload = () => {
  return false // 阻止自动上传
}

// 处理文件上传
const handleUpload = async () => {
  // 验证表单
  if (!uploadForm.docName.trim()) {
    ElMessage.error('请先输入文档名称')
    return
  }

  if (!selectedFile.value) {
    ElMessage.error('请先选择要上传的文件')
    return
  }

  try {
    uploading.value = true
    uploadProgress.value = 0
    elapsedTime.value = 0
    startTime.value = Date.now()

    // 创建FormData
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    formData.append('doc_name', uploadForm.docName)

    // 开始计时
    const timeInterval = setInterval(() => {
      elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000)
    }, 1000)

    // 模拟上传和处理进度
    let progressStage = 0
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 95) {
        // 不同阶段的进度速度不同
        let increment = 0
        if (progressStage === 0 && uploadProgress.value < 10) {
          // 上传阶段：快速
          increment = Math.random() * 5 + 2
        } else if (progressStage === 1 && uploadProgress.value < 20) {
          // 文件处理阶段：中等
          increment = Math.random() * 3 + 1
          progressStage = 1
        } else if (uploadProgress.value < 80) {
          // OCR识别阶段：较慢
          increment = Math.random() * 2 + 0.5
          progressStage = 2
        } else {
          // 最终处理阶段：很慢
          increment = Math.random() * 1 + 0.2
          progressStage = 3
        }

        uploadProgress.value = Math.min(95, uploadProgress.value + increment)
      }
    }, 800) // 增加间隔时间，让进度更平滑

    // 调用API上传
    const response = await ocrAPI.uploadImage(formData)

    clearInterval(progressInterval)
    clearInterval(timeInterval)
    uploadProgress.value = 100

    // 根据实际API返回格式处理结果
    // API返回格式: { "text": "识别的文本内容" }
    if (response && response.text) {
      ElMessage.success('文档识别成功！')

      // 使用工具函数格式化OCR结果
      ocrResult.value = formatOCRResult(response, uploadForm.docName, startTime.value)
    } else {
      // 处理错误情况
      ElMessage.error(response?.message || response?.msg || '识别失败，请重试')
    }
  } catch (error) {
    console.error('上传错误:', error)

    // 清理计时器
    if (typeof progressInterval !== 'undefined') clearInterval(progressInterval)
    if (typeof timeInterval !== 'undefined') clearInterval(timeInterval)

    // 使用工具函数获取友好的错误信息
    const message = getErrorMessage(error)
    ElMessage.error(message)
  } finally {
    uploading.value = false
  }
}

// 清除文件
const clearFile = () => {
  selectedFile.value = null
  previewImage.value = ''
  ocrResult.value = null
  uploadProgress.value = 0
}



// 复制识别结果
const copyResult = async () => {
  if (!ocrResult.value?.textContent) {
    ElMessage.warning('没有可复制的内容')
    return
  }

  try {
    await navigator.clipboard.writeText(ocrResult.value.textContent)
    ElMessage.success('文本已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动选择文本复制')
  }
}

// 导出识别结果
const downloadResult = () => {
  if (!ocrResult.value?.textContent) {
    ElMessage.warning('没有可导出的内容')
    return
  }

  const content = `文档名称: ${ocrResult.value.docName}\n识别时间: ${formatTime(ocrResult.value.createTime)}\n\n识别内容:\n${ocrResult.value.textContent}`
  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = `${ocrResult.value.docName}_OCR识别结果.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  ElMessage.success('文件导出成功')
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  const date = new Date(timeString)
  return date.toLocaleString('zh-CN')
}

// 格式化经过时间
const formatElapsedTime = (seconds) => {
  if (seconds < 60) {
    return `${seconds}秒`
  } else {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}分${remainingSeconds}秒`
  }
}

// 测试API连接
const testConnection = async () => {
  testing.value = true
  try {
    const result = await testOCRConnection()
    if (result.success) {
      ElMessage.success('API连接测试成功！服务器响应正常')
    } else {
      ElMessage.error(`连接测试失败: ${result.message}`)
    }
  } catch (error) {
    ElMessage.error('连接测试异常，请检查网络连接')
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.ocr-upload-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
}

.page-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

.upload-section {
  margin-bottom: 32px;
}

.upload-card,
.result-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1f2937;
}

.header-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.doc-name-section {
  margin-bottom: 24px;
}

.upload-area {
  margin-bottom: 24px;
}

.upload-dragger {
  width: 100%;
}

.upload-dragger :deep(.el-upload-dragger) {
  width: 100%;
  height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.upload-dragger :deep(.el-upload-dragger:hover) {
  border-color: #667eea;
  background: #f0f4ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
}

.upload-icon {
  opacity: 0.6;
}

.upload-text {
  text-align: center;
}

.upload-title {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.upload-time-hint {
  font-size: 13px;
  color: #f59e0b;
  margin: 0;
  font-weight: 500;
}

.upload-progress {
  margin-top: 16px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #6b7280;
}

.upload-actions {
  margin-top: 16px;
  text-align: center;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.progress-details {
  margin-top: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #6b7280;
}

.loading-icon {
  animation: spin 1s linear infinite;
  color: #409EFF;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.progress-time {
  font-size: 13px;
  color: #9ca3af;
  text-align: center;
}

.preview-section {
  border-top: 1px solid #e5e7eb;
  padding-top: 24px;
}

.preview-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.preview-container {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.preview-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-info {
  flex: 1;
}

.preview-info p {
  margin: 8px 0;
  font-size: 14px;
  color: #4b5563;
}

.result-section {
  margin-top: 32px;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.result-text h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.result-textarea :deep(.el-textarea__inner) {
  font-family: 'Courier New', monospace;
  line-height: 1.6;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .ocr-upload-container {
    padding: 16px;
  }
  
  .preview-container {
    flex-direction: column;
  }
  
  .preview-image {
    max-width: 100%;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
