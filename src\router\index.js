import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Dashboard from '../views/Dashboard.vue'
import OCRUpload from '../views/OCRUpload.vue'
import OCRHistory from '../views/OCRHistory.vue'
import DocumentSearch from '../views/DocumentSearch.vue'
import DocumentManagement from '../views/DocumentManagement.vue'
import APITest from '../views/APITest.vue'
import Test from '../views/Test.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresGuest: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/ocr',
    name: 'OCRUpload',
    component: OCRUpload,
    meta: { requiresAuth: true }
  },
  {
    path: '/history',
    name: 'OCRHistory',
    component: OCRHistory,
    meta: { requiresAuth: true }
  },
  {
    path: '/search',
    name: 'DocumentSearch',
    component: DocumentSearch,
    meta: { requiresAuth: true }
  },
  {
    path: '/documents',
    name: 'DocumentManagement',
    component: DocumentManagement,
    meta: { requiresAuth: true }
  },
  {
    path: '/api-test',
    name: 'APITest',
    component: APITest,
    meta: { requiresAuth: false } // 测试页面不需要认证
  },
  {
    path: '/test',
    name: 'Test',
    component: Test,
    meta: { requiresAuth: false } // 测试页面不需要认证
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 导航守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  const isAuthenticated = !!token

  console.log('🛣️ 路由导航:', {
    to: to.path,
    from: from.path,
    isAuthenticated,
    requiresAuth: to.meta.requiresAuth,
    requiresGuest: to.meta.requiresGuest,
    token: token ? '存在' : '不存在'
  })

  if (to.meta.requiresAuth && !isAuthenticated) {
    // 需要认证但未登录，跳转到首页
    console.log('❌ 需要认证但未登录，跳转到首页')
    next('/')
  } else if (to.meta.requiresGuest && isAuthenticated) {
    // 已登录用户访问首页，跳转到仪表板
    console.log('✅ 已登录用户访问首页，跳转到仪表板')
    next('/dashboard')
  } else {
    console.log('✅ 路由导航通过')
    next()
  }
})

export default router
