// 简单的HTML标签验证工具
const fs = require('fs');

function validateTemplate(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 提取template部分
  const templateMatch = content.match(/<template>([\s\S]*?)<\/template>/);
  if (!templateMatch) {
    console.log('❌ 没有找到template标签');
    return false;
  }
  
  const templateContent = templateMatch[1];
  
  // 简单的标签匹配检查
  const stack = [];
  const selfClosingTags = ['el-icon', 'el-input', 'el-button', 'el-tag', 'el-image', 'el-skeleton', 'el-empty'];
  
  // 匹配所有标签
  const tagRegex = /<\/?([a-zA-Z-]+)(?:\s[^>]*)?>/g;
  let match;
  let lineNumber = 1;
  
  while ((match = tagRegex.exec(templateContent)) !== null) {
    const fullTag = match[0];
    const tagName = match[1];
    
    // 计算行号
    const beforeMatch = templateContent.substring(0, match.index);
    lineNumber = (beforeMatch.match(/\n/g) || []).length + 1;
    
    if (fullTag.startsWith('</')) {
      // 闭合标签
      if (stack.length === 0) {
        console.log(`❌ 第${lineNumber}行: 多余的闭合标签 ${fullTag}`);
        return false;
      }
      
      const lastTag = stack.pop();
      if (lastTag !== tagName) {
        console.log(`❌ 第${lineNumber}行: 标签不匹配，期望 </${lastTag}>，实际 ${fullTag}`);
        return false;
      }
    } else if (fullTag.endsWith('/>') || selfClosingTags.includes(tagName)) {
      // 自闭合标签，不需要处理
      continue;
    } else {
      // 开始标签
      stack.push(tagName);
    }
  }
  
  if (stack.length > 0) {
    console.log(`❌ 未闭合的标签: ${stack.join(', ')}`);
    return false;
  }
  
  console.log('✅ 模板标签结构正确');
  return true;
}

// 运行验证
const filePath = 'src/views/OCRHistory.vue';
try {
  validateTemplate(filePath);
} catch (error) {
  console.error('验证失败:', error.message);
}
