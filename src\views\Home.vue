<template>
  <div class="home-container">
    <!-- 左侧内容区域 -->
    <div class="left-section">
      <div class="brand-area">
        <div class="logo-section">
          <div class="logo-icon">
            <el-icon size="48" color="#409EFF"><Document /></el-icon>
          </div>
          <div class="brand-text">
            <h1>纸质文件电子化系统</h1>
            <p>基于PaddleOCR的智能文档处理平台</p>
          </div>
        </div>

        <div class="features-showcase">
          <div class="feature-highlight">
            <el-icon size="24" color="#67C23A"><Check /></el-icon>
            <span>高精度OCR识别引擎</span>
          </div>
          <div class="feature-highlight">
            <el-icon size="24" color="#E6A23C"><Lightning /></el-icon>
            <span>快速批量文档处理</span>
          </div>
          <div class="feature-highlight">
            <el-icon size="24" color="#F56C6C"><Download /></el-icon>
            <span>多格式文件导出</span>
          </div>
          <div class="feature-highlight">
            <el-icon size="24" color="#909399"><Setting /></el-icon>
            <span>智能文档管理</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧认证区域 -->
    <div class="right-section">
      <div class="auth-container">
        <!-- 服务器状态提示 -->
        <ServerStatus v-if="showServerError" />

        <div class="auth-header">
          <h2>{{ activeTab === 'login' ? '欢迎回来' : '创建账户' }}</h2>
          <p>{{ activeTab === 'login' ? '登录您的账户以继续使用' : '注册新账户开始体验' }}</p>
        </div>

        <div class="tab-switcher">
          <button
            :class="['tab-btn', { active: activeTab === 'login' }]"
            @click="activeTab = 'login'"
          >
            登录
          </button>
          <button
            :class="['tab-btn', { active: activeTab === 'register' }]"
            @click="activeTab = 'register'"
          >
            注册
          </button>
        </div>

        <!-- 登录表单 -->
        <div v-show="activeTab === 'login'" class="form-container">
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="auth-form"
            @submit.prevent="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="用户名"
                size="large"
                :prefix-icon="User"
                class="form-input"
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="密码"
                size="large"
                :prefix-icon="Lock"
                show-password
                class="form-input"
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                class="auth-button"
                :loading="loginLoading"
                @click="handleLogin"
              >
                {{ loginLoading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>

            <!-- 测试跳转按钮 -->
          </el-form>
        </div>

        <!-- 注册表单 -->
        <div v-show="activeTab === 'register'" class="form-container">
          <el-form
            ref="registerFormRef"
            :model="registerForm"
            :rules="registerRules"
            class="auth-form"
            @submit.prevent="handleRegister"
          >
            <el-form-item prop="username">
              <el-input
                v-model="registerForm.username"
                placeholder="用户名"
                size="large"
                :prefix-icon="User"
                class="form-input"
              />
            </el-form-item>

            <el-form-item prop="email">
              <el-input
                v-model="registerForm.email"
                placeholder="邮箱地址"
                size="large"
                :prefix-icon="Message"
                class="form-input"
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="密码"
                size="large"
                :prefix-icon="Lock"
                show-password
                class="form-input"
              />
            </el-form-item>

            <el-form-item prop="confirmPassword">
              <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="确认密码"
                size="large"
                :prefix-icon="Lock"
                show-password
                class="form-input"
                @keyup.enter="handleRegister"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                class="auth-button"
                :loading="registerLoading"
                @click="handleRegister"
              >
                {{ registerLoading ? '注册中...' : '创建账户' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Message, Document, Check, Lightning, Download, Setting } from '@element-plus/icons-vue'
import { authAPI } from '../services/api'
import ServerStatus from '../components/ServerStatus.vue'
import { handleLoginSuccess, updateLastLoginTime } from '../utils/session'

const router = useRouter()
const activeTab = ref('login')
const loginFormRef = ref()
const registerFormRef = ref()
const loginLoading = ref(false)
const registerLoading = ref(false)
const showServerError = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 注册表单数据
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// 确认密码验证
const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 登录表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 注册表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  console.log('🔐 开始登录流程 (Home.vue)')

  if (!loginFormRef.value) {
    console.log('❌ 表单引用不存在')
    return
  }

  try {
    console.log('📝 开始表单验证')
    await loginFormRef.value.validate()
    console.log('✅ 表单验证通过')

    loginLoading.value = true

    console.log('🚀 发送登录请求:', {
      username: loginForm.username,
      password: '***'
    })

    const response = await authAPI.login({
      username: loginForm.username,
      password: loginForm.password
    })

    console.log('📥 收到登录响应:', response)
    console.log('📊 响应数据结构:', {
      success: response.success,
      token: response.token ? '存在' : '不存在',
      user: response.user ? '存在' : '不存在',
      msg: response.msg
    })

    // 简化处理逻辑，直接检查响应
    console.log('🔍 检查响应数据...')

    // 检查是否有token或success标志
    const hasToken = response.token || response.data?.token
    const isSuccess = response.success || response.data?.success || hasToken

    console.log('📋 登录状态检查:', {
      hasToken: !!hasToken,
      isSuccess: !!isSuccess,
      responseKeys: Object.keys(response)
    })

    if (isSuccess || hasToken) {
      console.log('✅ 登录成功，开始处理')

      // 直接保存token和用户信息
      const token = hasToken
      const user = response.user || response.data?.user || { username: loginForm.username }

      console.log('💾 保存认证信息:', {
        token: token ? '存在' : '不存在',
        user: user
      })

      localStorage.setItem('token', token)
      localStorage.setItem('user', JSON.stringify(user))

      // 验证保存是否成功
      const savedToken = localStorage.getItem('token')
      console.log('✅ Token保存验证:', savedToken ? '成功' : '失败')

      ElMessage.success('登录成功！')

      console.log('🎯 开始跳转到Dashboard...')

      try {
        await router.push('/dashboard')
        console.log('🔄 router.push 执行完成')
      } catch (routerError) {
        console.error('❌ 路由跳转错误:', routerError)
        // 尝试使用window.location跳转
        console.log('🔄 尝试使用window.location跳转')
        window.location.href = '/dashboard'
      }
    } else {
      console.log('❌ 登录失败，响应中没有有效的认证信息')
      console.log('📄 完整响应数据:', JSON.stringify(response, null, 2))
      ElMessage.error(response.msg || response.message || '登录失败，请检查用户名和密码')
    }
  } catch (error) {
    console.error('❌ 登录错误:', error)
    console.error('错误详情:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    })

    let message = '登录失败，请稍后重试'

    if (error.code === 'ERR_NETWORK') {
      message = '无法连接到服务器'
      showServerError.value = true
    } else if (error.response?.data?.msg) {
      message = error.response.data.msg
    } else if (error.response?.data?.message) {
      message = error.response.data.message
    }

    ElMessage.error(message)
  } finally {
    loginLoading.value = false
  }
}

// 测试跳转功能
const testJump = async () => {
  console.log('🧪 开始测试跳转功能')

  // 模拟设置token
  const testToken = 'test_token_' + Date.now()
  localStorage.setItem('token', testToken)
  localStorage.setItem('user', JSON.stringify({ username: 'test_user' }))

  console.log('💾 设置测试token:', testToken)
  console.log('🔍 验证token保存:', localStorage.getItem('token'))

  ElMessage.info('开始测试跳转...')

  try {
    console.log('🎯 尝试使用router.push跳转')
    await router.push('/dashboard')
    console.log('✅ router.push跳转成功')
  } catch (error) {
    console.error('❌ router.push跳转失败:', error)

    console.log('🔄 尝试使用window.location跳转')
    window.location.href = '/dashboard'
  }
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    await registerFormRef.value.validate()
    registerLoading.value = true

    const response = await authAPI.register({
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password
    })

    // 根据后端返回格式处理
    if (response.success) {
      ElMessage.success(response.msg || '注册成功！请登录')
      activeTab.value = 'login'

      // 清空注册表单
      registerForm.username = ''
      registerForm.email = ''
      registerForm.password = ''
      registerForm.confirmPassword = ''

      // 重置表单验证状态
      registerFormRef.value.resetFields()
    } else {
      ElMessage.error(response.msg || '注册失败')
    }
  } catch (error) {
    console.error('注册错误:', error)
    let message = '注册失败，请稍后重试'

    if (error.code === 'ERR_NETWORK') {
      message = '无法连接到服务器'
      showServerError.value = true
    } else if (error.response?.data?.msg) {
      message = error.response.data.msg
    } else if (error.response?.data?.message) {
      message = error.response.data.message
    }

    ElMessage.error(message)
  } finally {
    registerLoading.value = false
  }
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 左侧内容区域 */
.left-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
  min-height: 100vh;
}

.brand-area {
  max-width: 600px;
  color: white;
}

.logo-section {
  display: flex;
  align-items: center;
  margin-bottom: 60px;
}

.logo-icon {
  margin-right: 24px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.brand-text h1 {
  font-size: 42px;
  font-weight: 700;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.brand-text p {
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
}

.features-showcase {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.feature-highlight {
  display: flex;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-highlight:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.feature-highlight .el-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.feature-highlight span {
  font-size: 15px;
  font-weight: 500;
}

/* 右侧认证区域 */
.right-section {
  width: 480px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
}

.auth-container {
  width: 100%;
  max-width: 360px;
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
}

.auth-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.auth-header p {
  font-size: 15px;
  color: #666;
  margin: 0;
  font-weight: 400;
}

.tab-switcher {
  display: flex;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
  margin-bottom: 32px;
}

.tab-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-btn.active {
  background: white;
  color: #409EFF;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-container {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-form {
  width: 100%;
}

.form-input {
  margin-bottom: 8px;
}

.form-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 0 0 1px #e4e7ed;
  transition: all 0.2s ease;
}

.form-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc;
}

.form-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409EFF;
}

.auth-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  margin-top: 16px;
  background: linear-gradient(135deg, #409EFF 0%, #5a9cff 100%);
  border: none;
  transition: all 0.3s ease;
}

.auth-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .home-container {
    flex-direction: column;
  }

  .left-section {
    padding: 40px 20px;
    min-height: auto;
  }

  .right-section {
    width: 100%;
    padding: 40px 20px;
  }

  .brand-text h1 {
    font-size: 32px;
  }

  .features-showcase {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .left-section {
    padding: 30px 20px;
  }

  .logo-section {
    flex-direction: column;
    text-align: center;
    margin-bottom: 40px;
  }

  .logo-icon {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .brand-text h1 {
    font-size: 28px;
  }

  .brand-text p {
    font-size: 16px;
  }

  .right-section {
    padding: 30px 20px;
  }

  .auth-container {
    max-width: 100%;
  }
}
</style>
