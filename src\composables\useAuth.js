import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { authAPI } from '../services/api'

// 全局状态
const token = ref(localStorage.getItem('token'))
const user = ref(null)

// 初始化用户信息
const initUser = () => {
  const userStr = localStorage.getItem('user')
  if (userStr) {
    try {
      user.value = JSON.parse(userStr)
    } catch (error) {
      console.error('解析用户信息失败:', error)
      localStorage.removeItem('user')
    }
  }
}

// 初始化
initUser()

export function useAuth() {
  const router = useRouter()

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const currentUser = computed(() => user.value)

  // 登录
  const login = async (credentials) => {
    try {
      const response = await authAPI.login(credentials)

      if (response.success) {
        // 生成一个简单的token（实际项目中后端应该返回token）
        const sessionToken = `session_${credentials.username}_${Date.now()}`
        const userData = { username: credentials.username }

        token.value = sessionToken
        user.value = userData

        // 保存到本地存储
        localStorage.setItem('token', sessionToken)
        localStorage.setItem('user', JSON.stringify(userData))

        ElMessage.success(response.msg || '登录成功！')
        return true
      } else {
        ElMessage.error(response.msg || '登录失败，请检查用户名和密码')
        return false
      }
    } catch (error) {
      console.error('登录错误:', error)
      const message = error.response?.data?.msg ||
                     error.response?.data?.message ||
                     '登录失败，请稍后重试'
      ElMessage.error(message)
      return false
    }
  }

  // 注册
  const register = async (userData) => {
    try {
      const response = await authAPI.register(userData)

      // 根据后端返回的格式处理
      if (response.success) {
        ElMessage.success(response.msg || '注册成功！请登录')
        return true
      } else {
        ElMessage.error(response.msg || '注册失败')
        return false
      }
    } catch (error) {
      console.error('注册错误:', error)
      const message = error.response?.data?.msg || error.response?.data?.message || '注册失败，请稍后重试'
      ElMessage.error(message)
      return false
    }
  }

  // 退出登录
  const logout = () => {
    token.value = null
    user.value = null
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    
    ElMessage.success('已退出登录')
    router.push('/login')
  }

  // 检查认证状态
  const checkAuth = () => {
    const storedToken = localStorage.getItem('token')
    if (!storedToken) {
      return false
    }
    
    token.value = storedToken
    initUser()
    return true
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await authAPI.getUserInfo()
      user.value = response.user
      localStorage.setItem('user', JSON.stringify(user.value))
      return user.value
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能token已过期
      logout()
      return null
    }
  }

  return {
    // 状态
    token: computed(() => token.value),
    user: currentUser,
    isAuthenticated,
    
    // 方法
    login,
    register,
    logout,
    checkAuth,
    fetchUserInfo
  }
}
