// API路径测试工具
import axios from 'axios'

const baseURL = 'http://127.0.0.1:5000'

/**
 * 测试所有OCR相关API路径
 */
export const testAllOCRAPIs = async () => {
  const results = {
    timestamp: new Date().toISOString(),
    baseURL,
    tests: []
  }

  // 测试历史记录API
  const historyTest = await testHistoryAPI()
  results.tests.push(historyTest)

  // 测试上传API（只测试连通性，不实际上传）
  const uploadTest = await testUploadAPI()
  results.tests.push(uploadTest)

  // 汇总结果
  const successCount = results.tests.filter(t => t.success).length
  const totalCount = results.tests.length
  
  results.summary = {
    success: successCount,
    total: totalCount,
    successRate: `${((successCount / totalCount) * 100).toFixed(1)}%`,
    allPassed: successCount === totalCount
  }

  return results
}

/**
 * 测试历史记录API路径
 */
const testHistoryAPI = async () => {
  const test = {
    name: 'OCR历史记录API',
    path: '/api/ocr/history',
    method: 'GET',
    success: false,
    message: '',
    statusCode: null,
    responseTime: 0
  }

  const startTime = Date.now()
  
  try {
    const response = await axios.get(`${baseURL}/api/ocr/history`, {
      timeout: 5000,
      withCredentials: true
    })
    
    test.responseTime = Date.now() - startTime
    test.statusCode = response.status
    test.success = response.status === 200
    test.message = test.success ? 
      `API响应正常 (${test.responseTime}ms)` : 
      `HTTP ${response.status}`
      
    // 检查响应数据格式
    if (test.success) {
      const data = response.data
      if (Array.isArray(data) || (data && (data.data || data.list || data.records))) {
        test.message += ' - 数据格式正确'
      } else {
        test.message += ' - 数据格式可能异常'
      }
    }
  } catch (error) {
    test.responseTime = Date.now() - startTime
    test.statusCode = error.response?.status || null
    
    if (error.code === 'ERR_NETWORK') {
      test.message = '网络连接失败 - 服务器可能未启动'
    } else if (error.response?.status === 404) {
      test.message = 'API路径不存在 - 请检查后端路由配置'
    } else if (error.response?.status === 401) {
      test.message = '未授权访问 - 需要登录或session'
    } else if (error.response?.status === 500) {
      test.message = '服务器内部错误'
    } else {
      test.message = error.message || '未知错误'
    }
  }

  return test
}

/**
 * 测试上传API路径（仅测试连通性）
 */
const testUploadAPI = async () => {
  const test = {
    name: 'OCR上传API',
    path: '/image/ocr/upload',
    method: 'POST',
    success: false,
    message: '',
    statusCode: null,
    responseTime: 0
  }

  const startTime = Date.now()
  
  try {
    // 创建一个空的FormData来测试接口是否存在
    const formData = new FormData()
    
    const response = await axios.post(`${baseURL}/image/ocr/upload`, formData, {
      timeout: 5000,
      withCredentials: true,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    test.responseTime = Date.now() - startTime
    test.statusCode = response.status
    test.success = response.status === 200
    test.message = `API响应正常 (${test.responseTime}ms)`
  } catch (error) {
    test.responseTime = Date.now() - startTime
    test.statusCode = error.response?.status || null
    
    if (error.code === 'ERR_NETWORK') {
      test.message = '网络连接失败 - 服务器可能未启动'
    } else if (error.response?.status === 404) {
      test.message = 'API路径不存在 - 请检查后端路由配置'
    } else if (error.response?.status === 400) {
      // 400错误通常表示接口存在但参数不正确，这对于测试来说是好的
      test.success = true
      test.message = `API路径存在 (${test.responseTime}ms) - 参数验证正常`
    } else if (error.response?.status === 401) {
      test.success = true
      test.message = `API路径存在 (${test.responseTime}ms) - 需要认证`
    } else if (error.response?.status === 500) {
      test.message = '服务器内部错误'
    } else {
      test.message = error.message || '未知错误'
    }
  }

  return test
}

/**
 * 生成API测试报告的HTML
 */
export const generateTestReport = (results) => {
  const { summary, tests } = results
  
  let html = `
    <div style="font-family: monospace; padding: 20px; background: #f5f5f5;">
      <h2>OCR API路径测试报告</h2>
      <p><strong>测试时间:</strong> ${new Date(results.timestamp).toLocaleString('zh-CN')}</p>
      <p><strong>服务器地址:</strong> ${results.baseURL}</p>
      <p><strong>测试结果:</strong> ${summary.success}/${summary.total} 通过 (${summary.successRate})</p>
      
      <h3>详细结果:</h3>
      <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr style="background: #ddd;">
          <th style="padding: 8px;">API名称</th>
          <th style="padding: 8px;">路径</th>
          <th style="padding: 8px;">方法</th>
          <th style="padding: 8px;">状态</th>
          <th style="padding: 8px;">响应时间</th>
          <th style="padding: 8px;">消息</th>
        </tr>
  `
  
  tests.forEach(test => {
    const statusColor = test.success ? '#4CAF50' : '#f44336'
    const statusText = test.success ? '✅ 通过' : '❌ 失败'
    
    html += `
      <tr>
        <td style="padding: 8px;">${test.name}</td>
        <td style="padding: 8px; font-family: monospace;">${test.path}</td>
        <td style="padding: 8px;">${test.method}</td>
        <td style="padding: 8px; color: ${statusColor};">${statusText}</td>
        <td style="padding: 8px;">${test.responseTime}ms</td>
        <td style="padding: 8px;">${test.message}</td>
      </tr>
    `
  })
  
  html += `
      </table>
      
      <h3>建议:</h3>
      <ul>
  `
  
  if (summary.allPassed) {
    html += '<li style="color: #4CAF50;">✅ 所有API路径测试通过，系统运行正常</li>'
  } else {
    const failedTests = tests.filter(t => !t.success)
    failedTests.forEach(test => {
      if (test.message.includes('网络连接失败')) {
        html += '<li style="color: #f44336;">❌ 请确保后端服务正在运行</li>'
      } else if (test.message.includes('API路径不存在')) {
        html += `<li style="color: #f44336;">❌ 请检查后端路由配置: ${test.path}</li>`
      } else if (test.message.includes('未授权访问')) {
        html += '<li style="color: #ff9800;">⚠️ 请确保已正确登录或设置session</li>'
      }
    })
  }
  
  html += `
      </ul>
    </div>
  `
  
  return html
}

/**
 * 快速测试新的API路径
 */
export const quickTestNewAPI = async () => {
  console.log('🔍 测试新的API路径...')
  
  const results = await testAllOCRAPIs()
  
  console.log('📊 测试结果:', results)
  
  if (results.summary.allPassed) {
    console.log('✅ 所有API路径测试通过！')
  } else {
    console.log('❌ 部分API路径测试失败，请检查服务器状态')
  }
  
  return results
}
