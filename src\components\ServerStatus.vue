<template>
  <el-alert
    v-if="showServerAlert"
    title="后端服务连接提示"
    type="warning"
    :closable="true"
    @close="showServerAlert = false"
    class="server-alert"
  >
    <template #default>
      <div class="alert-content">
        <p><strong>无法连接到后端服务器 (http://127.0.0.1:5000)</strong></p>
        <p>请确保后端服务正在运行，或联系系统管理员。</p>
        <div class="server-info">
          <h4>后端API接口：</h4>
          <ul>
            <li>注册接口: POST /auth/register</li>
            <li>登录接口: POST /auth/login</li>
          </ul>
        </div>
      </div>
    </template>
  </el-alert>
</template>

<script setup>
import { ref } from 'vue'

const showServerAlert = ref(true)
</script>

<style scoped>
.server-alert {
  margin-bottom: 20px;
  border-radius: 8px;
}

.alert-content p {
  margin: 8px 0;
}

.server-info {
  margin-top: 12px;
  padding: 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

.server-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
}

.server-info ul {
  margin: 0;
  padding-left: 20px;
}

.server-info li {
  margin: 4px 0;
  font-size: 13px;
  color: #666;
}
</style>
