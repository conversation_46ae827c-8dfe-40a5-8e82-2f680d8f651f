<template>
  <div class="document-search">
    <div class="search-header">
      <h2>文档检索</h2>
      <p class="search-description">输入关键词搜索已处理的文档内容</p>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form @submit.prevent="handleSearch" class="search-form-container">
        <!-- 搜索类型选择 -->
        <div class="search-type-section">
          <el-radio-group v-model="searchType" size="large" class="search-type-radio">
            <el-radio-button value="keyword">
              <el-icon><Search /></el-icon>
              关键词检索
            </el-radio-button>
            <el-radio-button value="semantic">
              <el-icon><MagicStick /></el-icon>
              语义检索
            </el-radio-button>
          </el-radio-group>
        </div>

        <!-- 搜索输入区域 -->
        <div class="search-input-section">
          <el-input
            v-model="searchQuery"
            :placeholder="searchType === 'semantic' ? '请输入语义问题，如：如何提高学习效率？' : '请输入检索关键词'"
            size="large"
            class="search-input"
            @keyup.enter="handleSearch"
            :loading="searching"
          >
            <template #prepend>
              <el-icon>
                <Search v-if="searchType === 'keyword'" />
                <MagicStick v-else />
              </el-icon>
            </template>
          </el-input>

          <!-- 语义检索参数 -->
          <div class="search-params" v-if="searchType === 'semantic'">
            <el-form-item label="返回结果数量:">
              <el-input-number
                v-model="semanticK"
                :min="1"
                :max="20"
                size="small"
                style="width: 100px"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 搜索按钮 -->
        <div class="search-button-section">
          <el-button
            type="primary"
            size="large"
            @click="handleSearch"
            :loading="searching"
            :disabled="!searchQuery.trim()"
            class="search-button"
          >
            <el-icon>
              <Search v-if="searchType === 'keyword'" />
              <MagicStick v-else />
            </el-icon>
            {{ searchType === 'semantic' ? '语义搜索' : '关键词搜索' }}
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 搜索结果 -->
    <div class="search-results" v-if="hasSearched">
      <!-- 结果统计 -->
      <div class="results-header" v-if="searchResults.length > 0">
        <p class="results-count">
          找到 <strong>{{ searchResults.length }}</strong> 条相关结果
          <span class="search-time" v-if="searchTime">（用时 {{ searchTime }}ms）</span>
        </p>

      </div>

      <!-- 无结果提示 -->
      <div class="no-results" v-if="searchResults.length === 0 && !searching">
        <el-empty description="未找到相关文档">
          <template #image>
            <el-icon size="60" color="#909399"><DocumentRemove /></el-icon>
          </template>
          <p>尝试使用不同的关键词进行搜索</p>
        </el-empty>
      </div>

      <!-- 结果列表 -->
      <div class="results-list" v-if="searchResults.length > 0" ref="resultsList">


        <div
          v-for="(result, index) in searchResults"
          :key="index"
          class="result-item"
        >
          <div class="result-card">
            <div class="result-header">
              <div class="result-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="result-title-section">
                <h3 class="result-title">
                  {{ getDocumentTitle(result, index) }}
                </h3>
                <div class="result-meta">
                  <el-tag
                    size="small"
                    :type="searchType === 'semantic' ? 'warning' : 'success'"
                    effect="light"
                    v-if="result.score"
                  >
                    <el-icon>
                      <MagicStick v-if="searchType === 'semantic'" />
                      <Star v-else />
                    </el-icon>
                    {{ searchType === 'semantic' ? '相似度' : '相关度' }}: {{ (result.score * 100).toFixed(1) }}%
                  </el-tag>
                  <el-tag
                    size="small"
                    :type="searchType === 'semantic' ? 'warning' : 'primary'"
                    effect="plain"
                  >
                    {{ searchType === 'semantic' ? '语义搜索' : '关键词搜索' }}
                  </el-tag>
                  <span class="result-date" v-if="result.created_at">
                    <el-icon><Clock /></el-icon>
                    {{ formatDate(result.created_at) }}
                  </span>
                </div>
              </div>
            </div>

            <div class="result-content">
              <div class="result-snippet" v-html="highlightKeywords(result.full_text || result.content || result.text || '暂无内容预览')"></div>
            </div>

            <div class="result-actions">
              <el-button
                size="small"
                type="primary"
                :icon="View"
                @click="viewDocument(result)"
                v-if="result.doc_name"
              >
                查看详情
              </el-button>
              <el-button
                size="small"
                type="success"
                :icon="Download"
                @click="downloadPDF(result)"
                v-if="result.doc_name"
                :loading="downloadingPDF === result.doc_name"
              >
                下载PDF
              </el-button>
              <el-button
                size="small"
                :icon="CopyDocument"
                @click="copyContent(result.full_text || result.content || result.text)"
                v-if="result.full_text || result.content || result.text"
              >
                复制内容
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 回到顶部按钮 -->
      <el-backtop
        target=".results-list"
        :bottom="20"
        :right="20"
        v-if="searchResults.length > 3"
      >
        <div class="back-to-top">
          <el-icon><ArrowUp /></el-icon>
        </div>
      </el-backtop>
    </div>

    <!-- 加载状态 -->
    <div class="loading-state" v-if="searching">
      <div class="loading-cards">
        <div v-for="i in 3" :key="i" class="loading-card">
          <div class="loading-header">
            <div class="loading-icon"></div>
            <div class="loading-title-section">
              <div class="loading-title"></div>
              <div class="loading-meta"></div>
            </div>
          </div>
          <div class="loading-content">
            <div class="loading-line"></div>
            <div class="loading-line"></div>
            <div class="loading-line short"></div>
          </div>
          <div class="loading-actions">
            <div class="loading-button"></div>
            <div class="loading-button"></div>
          </div>
        </div>
      </div>
      <p class="loading-text">
        <el-icon class="loading-icon-spin"><Loading /></el-icon>
        正在搜索文档...
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Document, DocumentRemove, ArrowUp, Star, Clock, View, CopyDocument, Loading, MagicStick, Download } from '@element-plus/icons-vue'
import { searchAPI, ocrAPI } from '../services/api'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const searchType = ref('keyword') // 'keyword' 或 'semantic'
const semanticK = ref(5) // 语义检索返回结果数量
const searching = ref(false)
const hasSearched = ref(false)
const searchResults = ref([])
const searchTime = ref(0)
const resultsList = ref(null)
const downloadingPDF = ref(null) // 当前正在下载的文档名

// 执行搜索
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    ElMessage.warning(searchType.value === 'semantic' ? '请输入语义问题' : '请输入检索关键词')
    return
  }

  searching.value = true
  hasSearched.value = true
  const startTime = Date.now()

  try {
    let response
    const query = searchQuery.value.trim()

    if (searchType.value === 'semantic') {
      console.log('🧠 开始语义搜索:', { query, k: semanticK.value })
      response = await searchAPI.semanticSearch(query, semanticK.value)
    } else {
      console.log('🔍 开始关键词搜索:', { query })
      response = await searchAPI.searchDocuments(query)
    }

    searchTime.value = Date.now() - startTime

    console.log(`${searchType.value === 'semantic' ? '🧠' : '🔍'} 搜索响应:`, response)

    // 由于axios拦截器返回response.data，所以response就是实际的数据
    if (response && response.results && Array.isArray(response.results)) {
      searchResults.value = response.results
      console.log('✅ 找到', response.results.length, '条搜索结果')
      ElMessage.success(`${searchType.value === 'semantic' ? '语义搜索' : '关键词搜索'}找到 ${response.results.length} 条相关结果`)

      // 滚动到结果列表顶部
      setTimeout(() => {
        if (resultsList.value) {
          resultsList.value.scrollTop = 0
        }
      }, 100)
    } else if (response && Array.isArray(response)) {
      searchResults.value = response
      console.log('✅ 找到', response.length, '条搜索结果')
      ElMessage.success(`${searchType.value === 'semantic' ? '语义搜索' : '关键词搜索'}找到 ${response.length} 条相关结果`)

      // 滚动到结果列表顶部
      setTimeout(() => {
        if (resultsList.value) {
          resultsList.value.scrollTop = 0
        }
      }, 100)
    } else {
      searchResults.value = []
      console.log('❌ 未找到搜索结果')
      ElMessage.info('未找到相关文档')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    searchResults.value = []

    let errorMessage = `${searchType.value === 'semantic' ? '语义搜索' : '关键词搜索'}失败`
    if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    searching.value = false
  }
}

// 高亮关键词
const highlightKeywords = (text) => {
  if (!searchQuery.value.trim() || !text) return text
  
  const keywords = searchQuery.value.trim().split(/\s+/)
  let highlightedText = text
  
  keywords.forEach(keyword => {
    const regex = new RegExp(`(${keyword})`, 'gi')
    highlightedText = highlightedText.replace(regex, '<mark>$1</mark>')
  })
  
  return highlightedText
}

// 获取文档标题
const getDocumentTitle = (result, index) => {
  if (result.doc_name) {
    // 尝试从doc_name中提取更友好的标题
    const docName = result.doc_name
    // 如果包含时间戳格式，尝试提取前面的部分
    const match = docName.match(/^(\d{8}_\d{6}_\d+)/)
    if (match) {
      const timestamp = match[1]
      const date = timestamp.substring(0, 8)
      const time = timestamp.substring(9, 15)
      return `文档 ${date.substring(0,4)}-${date.substring(4,6)}-${date.substring(6,8)} ${time.substring(0,2)}:${time.substring(2,4)}:${time.substring(4,6)}`
    }
    return docName
  }
  return result.document_name || result.filename || `文档 ${index + 1}`
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 查看文档详情
const viewDocument = (result) => {
  if (result.doc_name) {
    // 从doc_name中提取文档名和文件名
    // 格式通常是: "日期_时间_序号__时间戳_随机码"
    const docName = result.doc_name

    // 尝试解析文档名，如果无法解析则直接使用doc_name
    router.push({
      name: 'OCRHistory',
      query: {
        docName: docName,
        // 如果有单独的filename字段则使用，否则使用doc_name
        filename: result.filename || docName
      }
    })
  } else if (result.document_name && result.filename) {
    router.push({
      name: 'OCRHistory',
      query: {
        docName: result.document_name,
        filename: result.filename
      }
    })
  }
}

// 下载PDF文档
const downloadPDF = async (result) => {
  if (!result.doc_name) {
    ElMessage.warning('无法获取文档信息')
    return
  }

  downloadingPDF.value = result.doc_name

  try {
    console.log('📄 开始下载PDF:', result.doc_name)
    console.log('📄 完整结果对象:', result)
    ElMessage.info('正在生成PDF文档，请稍候...')

    const response = await ocrAPI.downloadPDF(result.doc_name)

    // 创建下载链接
    const blob = new Blob([response], { type: 'application/pdf' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名
    const fileName = `${result.doc_name}_ocr.pdf`
    link.download = fileName

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    window.URL.revokeObjectURL(url)

    ElMessage.success('PDF文档下载成功')
    console.log('✅ PDF下载完成:', fileName)

  } catch (error) {
    console.error('PDF下载失败:', error)

    let errorMessage = 'PDF下载失败'
    if (error.response?.status === 404) {
      errorMessage = '文档不存在或PDF尚未生成'
    } else if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    downloadingPDF.value = null
  }
}

// 复制内容
const copyContent = async (content) => {
  if (!content) {
    ElMessage.warning('没有可复制的内容')
    return
  }

  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('内容已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级方案：使用传统的复制方法
    try {
      const textArea = document.createElement('textarea')
      textArea.value = content
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('内容已复制到剪贴板')
    } catch (fallbackError) {
      console.error('降级复制也失败:', fallbackError)
      ElMessage.error('复制失败，请手动复制')
    }
  }
}
</script>

<style scoped>
.document-search {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-header {
  text-align: center;
  margin-bottom: 30px;
}

.search-header h2 {
  color: #303133;
  margin-bottom: 10px;
}

.search-description {
  color: #606266;
  font-size: 14px;
}

.search-form {
  margin-bottom: 30px;
}

.search-form-container {
  max-width: 800px;
  margin: 0 auto;
}

.search-type-section {
  margin-bottom: 20px;
  text-align: center;
}

.search-type-radio {
  display: inline-flex;
  background: white;
  border-radius: 12px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.search-type-radio .el-radio-button__inner {
  border: none;
  background: transparent;
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-type-radio .el-radio-button__original-radio:checked + .el-radio-button__inner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.search-input-section {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  margin-bottom: 12px;
}

.search-input .el-input__inner {
  border-radius: 12px;
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;
  font-size: 16px;
  padding: 12px 16px;
}

.search-input .el-input__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-params {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.search-params .el-form-item {
  margin: 0;
}

.search-params .el-form-item__label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.search-button-section {
  text-align: center;
}

.search-button {
  padding: 12px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.search-button:disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.search-results {
  margin-top: 20px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.results-header {
  margin-bottom: 20px;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.results-count {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.search-time {
  color: #909399;
  font-size: 12px;
}

.no-results {
  text-align: center;
  padding: 40px 0;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  max-height: calc(100vh - 300px);
}

/* 自定义滚动条样式 */
.results-list::-webkit-scrollbar {
  width: 6px;
}

.results-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 结果项样式 */
.result-item {
  position: relative;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

.result-item:nth-child(1) { animation-delay: 0.1s; }
.result-item:nth-child(2) { animation-delay: 0.2s; }
.result-item:nth-child(3) { animation-delay: 0.3s; }
.result-item:nth-child(4) { animation-delay: 0.4s; }
.result-item:nth-child(5) { animation-delay: 0.5s; }
.result-item:nth-child(n+6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.result-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-color: #e0e0e0;
}

.result-card:hover::before {
  opacity: 1;
}

/* 结果头部 */
.result-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.result-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.result-title-section {
  flex: 1;
  min-width: 0;
}

.result-title {
  color: #1a1a1a;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.4;
  word-break: break-word;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.result-date {
  color: #8a8a8a;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 结果内容 */
.result-content {
  margin-bottom: 20px;
}

.result-snippet {
  color: #4a4a4a;
  line-height: 1.7;
  margin: 0;
  font-size: 14px;
  max-height: 140px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  word-break: break-word;
  white-space: pre-wrap;
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  border-left: 3px solid #e0e0e0;
}

.result-snippet :deep(mark) {
  background: linear-gradient(135deg, #fff2cc 0%, #ffe066 100%);
  color: #d97706;
  padding: 3px 6px;
  border-radius: 4px;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(217, 119, 6, 0.2);
}

/* 操作按钮 */
.result-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.result-actions .el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.result-actions .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.result-actions .el-button--primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.result-actions .el-button:not(.el-button--primary) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #495057;
}

.result-actions .el-button:not(.el-button--primary):hover {
  background: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

.result-actions .el-button--success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.result-actions .el-button--success:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.loading-state {
  padding: 20px 0;
}

.loading-cards {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.loading-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.loading-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.loading-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 12px;
}

.loading-title-section {
  flex: 1;
}

.loading-title {
  height: 20px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
  width: 60%;
}

.loading-meta {
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  width: 40%;
}

.loading-content {
  margin-bottom: 20px;
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  border-left: 3px solid #e0e0e0;
}

.loading-line {
  height: 14px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
}

.loading-line.short {
  width: 70%;
}

.loading-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.loading-button {
  width: 80px;
  height: 32px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 8px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading-text {
  color: #909399;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
}

.loading-icon-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 回到顶部按钮样式 */
.back-to-top {
  width: 40px;
  height: 40px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.back-to-top:hover {
  background: #337ecc;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

@media (max-width: 768px) {
  .document-search {
    padding: 16px;
    height: 100vh;
  }

  .search-form-container {
    max-width: 100%;
  }

  .search-type-radio {
    flex-direction: column;
    width: 100%;
  }

  .search-type-radio .el-radio-button {
    flex: 1;
  }

  .search-type-radio .el-radio-button__inner {
    width: 100%;
    justify-content: center;
  }

  .search-input {
    font-size: 16px; /* 防止iOS缩放 */
  }

  .search-params {
    flex-direction: column;
    gap: 8px;
  }

  .search-button {
    width: 100%;
    padding: 14px 24px;
  }

  .results-list {
    max-height: calc(100vh - 250px);
    padding-right: 4px;
    gap: 16px;
  }

  .result-card {
    padding: 20px;
    border-radius: 12px;
  }

  .result-header {
    gap: 12px;
  }

  .result-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .result-title {
    font-size: 16px;
  }

  .result-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .result-snippet {
    font-size: 13px;
    padding: 12px;
    max-height: 120px;
    -webkit-line-clamp: 5;
  }

  .result-actions {
    flex-direction: column;
    gap: 8px;
  }

  .result-actions .el-button {
    width: 100%;
    justify-content: center;
  }

  /* 移动端滚动条样式 */
  .results-list::-webkit-scrollbar {
    width: 4px;
  }
}
</style>
