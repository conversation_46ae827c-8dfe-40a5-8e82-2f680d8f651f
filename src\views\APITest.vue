<template>
  <div class="api-test-container">
    <div class="page-header">
      <h2>API连接测试</h2>
      <p>测试后端API连接状态和功能</p>
    </div>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>连接测试</span>
          <el-button type="primary" @click="runAllTests" :loading="testing">
            <el-icon><Connection /></el-icon>
            运行所有测试
          </el-button>
        </div>
      </template>

      <div class="test-section">
        <h3>基础连接测试</h3>
        <div class="test-item">
          <el-button @click="testDirectConnection" :loading="testingDirect">
            测试直接连接 (http://127.0.0.1:5000)
          </el-button>
          <span v-if="directResult" :class="['result', directResult.success ? 'success' : 'error']">
            {{ directResult.message }}
          </span>
        </div>

        <div class="test-item">
          <el-button @click="testProxyConnection" :loading="testingProxy">
            测试代理连接 (/api)
          </el-button>
          <span v-if="proxyResult" :class="['result', proxyResult.success ? 'success' : 'error']">
            {{ proxyResult.message }}
          </span>
        </div>

        <div class="test-item">
          <el-button @click="testHistoryAPI" :loading="testingHistory">
            测试历史记录API
          </el-button>
          <span v-if="historyResult" :class="['result', historyResult.success ? 'success' : 'error']">
            {{ historyResult.message }}
          </span>
        </div>

        <div class="test-item">
          <el-button @click="testHistoryDetailAPI" :loading="testingDetail">
            测试历史详情API
          </el-button>
          <span v-if="detailResult" :class="['result', detailResult.success ? 'success' : 'error']">
            {{ detailResult.message }}
          </span>
        </div>

        <div class="test-item">
          <el-button @click="testSearchAPI" :loading="testingSearch">
            测试文档检索API
          </el-button>
          <span v-if="searchResult" :class="['result', searchResult.success ? 'success' : 'error']">
            {{ searchResult.message }}
          </span>
        </div>

        <div class="test-item">
          <el-button @click="testSemanticSearchAPI" :loading="testingSemantic">
            测试语义检索API
          </el-button>
          <span v-if="semanticResult" :class="['result', semanticResult.success ? 'success' : 'error']">
            {{ semanticResult.message }}
          </span>
        </div>

        <div class="test-item">
          <el-button @click="testPDFDownloadAPI" :loading="testingPDFDownload">
            测试PDF下载API
          </el-button>
          <span v-if="pdfDownloadResult" :class="['result', pdfDownloadResult.success ? 'success' : 'error']">
            {{ pdfDownloadResult.message }}
          </span>
        </div>
      </div>

      <div class="test-section" v-if="testResults.length > 0">
        <h3>测试结果</h3>
        <div class="results-container">
          <div v-for="(result, index) in testResults" :key="index" class="result-item">
            <div class="result-header">
              <span :class="['status-icon', result.success ? 'success' : 'error']">
                {{ result.success ? '✅' : '❌' }}
              </span>
              <span class="test-name">{{ result.name }}</span>
              <span class="test-time">{{ formatTime(result.timestamp) }}</span>
            </div>
            <div class="result-message">{{ result.message }}</div>
            <div v-if="result.data" class="result-data">
              <el-collapse>
                <el-collapse-item title="查看详细数据">
                  <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <el-card class="info-card">
      <template #header>
        <span>调试信息</span>
      </template>
      <div class="debug-info">
        <p><strong>前端地址:</strong> {{ window.location.origin }}</p>
        <p><strong>后端地址:</strong> http://127.0.0.1:5000</p>
        <p><strong>代理配置:</strong> /api → http://127.0.0.1:5000</p>
        <p><strong>当前时间:</strong> {{ currentTime }}</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Connection } from '@element-plus/icons-vue'
import axios from 'axios'
import { ocrAPI, searchAPI } from '../services/api'

// 响应式数据
const testing = ref(false)
const testingDirect = ref(false)
const testingProxy = ref(false)
const testingHistory = ref(false)
const testingDetail = ref(false)
const testingSearch = ref(false)
const testingSemantic = ref(false)
const testingPDFDownload = ref(false)
const currentTime = ref('')

const directResult = ref(null)
const proxyResult = ref(null)
const historyResult = ref(null)
const detailResult = ref(null)
const searchResult = ref(null)
const semanticResult = ref(null)
const pdfDownloadResult = ref(null)
const testResults = ref([])

// 更新当前时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})

// 测试直接连接
const testDirectConnection = async () => {
  testingDirect.value = true
  try {
    const response = await axios.get('http://127.0.0.1:5000/api/ocr/history?page=1&size=1', {
      timeout: 60000, // 1分钟超时，适应OCR处理时间
      withCredentials: false
    })
    
    directResult.value = {
      success: true,
      message: `连接成功，状态码: ${response.status}`
    }
    
    addTestResult('直接连接测试', true, '连接成功', response.data)
  } catch (error) {
    directResult.value = {
      success: false,
      message: `连接失败: ${error.message}`
    }
    
    addTestResult('直接连接测试', false, error.message, null)
  } finally {
    testingDirect.value = false
  }
}

// 测试代理连接
const testProxyConnection = async () => {
  testingProxy.value = true
  try {
    const response = await axios.get('/api/ocr/history?page=1&size=1', {
      timeout: 60000, // 1分钟超时，适应OCR处理时间
      withCredentials: false
    })
    
    proxyResult.value = {
      success: true,
      message: `代理连接成功，状态码: ${response.status}`
    }
    
    addTestResult('代理连接测试', true, '代理连接成功', response.data)
  } catch (error) {
    proxyResult.value = {
      success: false,
      message: `代理连接失败: ${error.message}`
    }
    
    addTestResult('代理连接测试', false, error.message, null)
  } finally {
    testingProxy.value = false
  }
}

// 测试历史记录API
const testHistoryAPI = async () => {
  testingHistory.value = true
  try {
    const response = await ocrAPI.getHistory({ page: 1, size: 1 })

    historyResult.value = {
      success: true,
      message: `API调用成功，返回 ${response?.history?.length || 0} 条记录`
    }

    addTestResult('历史记录API测试', true, 'API调用成功', response)
  } catch (error) {
    historyResult.value = {
      success: false,
      message: `API调用失败: ${error.message}`
    }

    addTestResult('历史记录API测试', false, error.message, null)
  } finally {
    testingHistory.value = false
  }
}

// 测试历史详情API
const testHistoryDetailAPI = async () => {
  testingDetail.value = true
  try {
    // 先获取历史记录列表，然后测试详情API
    const historyResponse = await ocrAPI.getHistory({ page: 1, size: 1 })

    if (historyResponse?.history?.length > 0) {
      const firstRecord = historyResponse.history[0]
      const detailResponse = await ocrAPI.getHistoryDetail(firstRecord.doc_name, firstRecord.filename)

      detailResult.value = {
        success: true,
        message: `详情API调用成功，文档: ${firstRecord.doc_name}`
      }

      addTestResult('历史详情API测试', true, '详情API调用成功', {
        request: { doc_name: firstRecord.doc_name, filename: firstRecord.filename },
        response: detailResponse
      })
    } else {
      detailResult.value = {
        success: false,
        message: '没有历史记录可供测试详情API'
      }

      addTestResult('历史详情API测试', false, '没有历史记录', null)
    }
  } catch (error) {
    detailResult.value = {
      success: false,
      message: `详情API调用失败: ${error.message}`
    }

    addTestResult('历史详情API测试', false, error.message, null)
  } finally {
    testingDetail.value = false
  }
}

// 测试检索API
const testSearchAPI = async () => {
  testingSearch.value = true
  try {
    console.log('🔍 开始测试检索API...')
    const testQuery = '测试'
    const response = await searchAPI.searchDocuments(testQuery)

    searchResult.value = {
      success: true,
      message: `检索API测试成功，查询: "${testQuery}"`
    }

    addTestResult('文档检索API测试', true, `检索成功，查询: "${testQuery}"`, response)
    ElMessage.success('检索API测试成功')
  } catch (error) {
    console.error('检索API测试失败:', error)

    let errorMessage = '检索API测试失败'
    if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    searchResult.value = {
      success: false,
      message: errorMessage
    }

    addTestResult('文档检索API测试', false, errorMessage, null)
  } finally {
    testingSearch.value = false
  }
}

// 测试语义检索API
const testSemanticSearchAPI = async () => {
  testingSemantic.value = true
  try {
    console.log('🧠 开始测试语义检索API...')
    const testQuery = '如何提高学习效率'
    const testK = 3
    const response = await searchAPI.semanticSearch(testQuery, testK)

    semanticResult.value = {
      success: true,
      message: `语义检索API测试成功，查询: "${testQuery}", k=${testK}`
    }

    addTestResult('语义检索API测试', true, `语义检索成功，查询: "${testQuery}", k=${testK}`, response)
    ElMessage.success('语义检索API测试成功')
  } catch (error) {
    console.error('语义检索API测试失败:', error)

    let errorMessage = '语义检索API测试失败'
    if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    semanticResult.value = {
      success: false,
      message: errorMessage
    }

    addTestResult('语义检索API测试', false, errorMessage, null)
  } finally {
    testingSemantic.value = false
  }
}

// 测试PDF下载API
const testPDFDownloadAPI = async () => {
  testingPDFDownload.value = true
  try {
    console.log('📄 开始测试PDF下载API...')
    const testDocName = 'test_doc' // 使用一个测试文档名
    console.log('📄 测试文档名称:', testDocName)
    console.log('📄 将请求URL: /api/ocr/pdf/' + encodeURIComponent(testDocName))

    // 注意：这里只是测试API调用，不会实际下载文件
    const response = await ocrAPI.downloadPDF(testDocName)

    pdfDownloadResult.value = {
      success: true,
      message: `PDF下载API测试成功，文档: "${testDocName}"`
    }

    addTestResult('PDF下载API测试', true, `PDF下载API响应正常，文档: "${testDocName}"`, {
      responseType: typeof response,
      size: response?.size || 'unknown'
    })
    ElMessage.success('PDF下载API测试成功')
  } catch (error) {
    console.error('PDF下载API测试失败:', error)

    let errorMessage = 'PDF下载API测试失败'
    if (error.response?.status === 404) {
      errorMessage = '测试文档不存在（这是正常的）'
      // 404错误在测试中是可以接受的
      pdfDownloadResult.value = {
        success: true,
        message: errorMessage
      }
      addTestResult('PDF下载API测试', true, errorMessage, null)
    } else {
      if (error.response?.data?.msg) {
        errorMessage = error.response.data.msg
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.message) {
        errorMessage = error.message
      }

      pdfDownloadResult.value = {
        success: false,
        message: errorMessage
      }

      addTestResult('PDF下载API测试', false, errorMessage, null)
    }
  } finally {
    testingPDFDownload.value = false
  }
}

// 运行所有测试
const runAllTests = async () => {
  testing.value = true
  testResults.value = []

  ElMessage.info('开始运行所有测试...')

  await testDirectConnection()
  await new Promise(resolve => setTimeout(resolve, 500))

  await testProxyConnection()
  await new Promise(resolve => setTimeout(resolve, 500))

  await testHistoryAPI()
  await new Promise(resolve => setTimeout(resolve, 500))

  await testHistoryDetailAPI()
  await new Promise(resolve => setTimeout(resolve, 500))

  await testSearchAPI()
  await new Promise(resolve => setTimeout(resolve, 500))

  await testSemanticSearchAPI()
  await new Promise(resolve => setTimeout(resolve, 500))

  await testPDFDownloadAPI()

  testing.value = false

  const successCount = testResults.value.filter(r => r.success).length
  const totalCount = testResults.value.length

  if (successCount === totalCount) {
    ElMessage.success(`所有测试通过 (${successCount}/${totalCount})`)
  } else {
    ElMessage.warning(`部分测试失败 (${successCount}/${totalCount})`)
  }
}

// 添加测试结果
const addTestResult = (name, success, message, data) => {
  testResults.value.push({
    name,
    success,
    message,
    data,
    timestamp: new Date()
  })
}

// 格式化时间
const formatTime = (date) => {
  return date.toLocaleTimeString('zh-CN')
}
</script>

<style scoped>
.api-test-container {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

.test-card,
.info-card {
  margin-bottom: 24px;
  border-radius: 12px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #1f2937;
}

.test-section {
  margin-bottom: 24px;
}

.test-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.result.success {
  color: #10b981;
  font-weight: 500;
}

.result.error {
  color: #ef4444;
  font-weight: 500;
}

.results-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.status-icon.success {
  color: #10b981;
}

.status-icon.error {
  color: #ef4444;
}

.test-name {
  font-weight: 600;
  color: #1f2937;
}

.test-time {
  color: #6b7280;
  font-size: 14px;
  margin-left: auto;
}

.result-message {
  color: #374151;
  margin-bottom: 8px;
}

.result-data pre {
  background: #f8fafc;
  padding: 12px;
  border-radius: 6px;
  font-size: 12px;
  overflow-x: auto;
}

.debug-info p {
  margin: 8px 0;
  color: #374151;
}

.debug-info strong {
  color: #1f2937;
}
</style>
